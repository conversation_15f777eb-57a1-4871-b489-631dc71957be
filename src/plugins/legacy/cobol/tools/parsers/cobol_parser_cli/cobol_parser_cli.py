#!/usr/bin/env python3

"""COBOL Parser for IBM ENTERPRISE COBOL source code.

This program parses COBOL source files, builds an internal representation,
and stores the parsed data in a graph database database.
"""

import os
import sys

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src import main

if __name__ == '__main__':
    main()
