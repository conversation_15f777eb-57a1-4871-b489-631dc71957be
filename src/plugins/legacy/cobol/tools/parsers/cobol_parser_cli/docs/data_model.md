# Neo4j Data Model Documentation

This document provides comprehensive information about the Neo4j data model used in the COBOL Parser and Call Tree Analysis tool. The model is designed to represent COBOL program structures and their execution flow, enabling static code analysis and relationship visualization.

## Overview

The Neo4j data model consists of two sets of nodes and relationships:

1. **Base Structure** (created by the parser): Represents the structural elements of COBOL programs
2. **Call Tree** (created by the call_tree analyzer): Represents execution flow and relationships between elements

## Node Types

### Parser-Created Nodes

#### CobolModule
**Description**: Represents a single COBOL program/module.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID/name of the COBOL module
- `file_name` (String): Source file name

#### CobolIdentificationDivision
**Description**: Represents the Identification Division of a COBOL program.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `program_id` (String): PROGRAM-ID value
- `full_text` (String): Raw text of the division

#### CobolEnvironmentDivision
**Description**: Represents the Environment Division of a COBOL program.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `full_text` (String): Raw text of the division

#### CobolDataDivision
**Description**: Represents the Data Division of a COBOL program.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `full_text` (String): Raw text of the division

#### CobolProcedureDivision
**Description**: Represents the Procedure Division of a COBOL program.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `full_text` (String): Raw text of the division

#### CobolSection
**Description**: Represents a section within the Procedure Division.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `section_name` (String): Name of the section
- `is_unnamed` (Boolean): Indicates if the section has no explicit name
- `full_text` (String): Raw text of the section

#### CobolParagraph
**Description**: Represents a paragraph within the Procedure Division or a section.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `paragraph_name` (String): Name of the paragraph
- `is_unnamed` (Boolean): Indicates if the paragraph has no explicit name
- `full_text` (String): Raw text of the paragraph

#### CobolEntryParagraph
**Description**: Represents the main entry point of a COBOL program.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID of the parent module
- `paragraph_name` (String): Name of the entry paragraph
- `is_main_entry` (Boolean): Indicates if this is the main entry point
- `full_text` (String): Raw text of the entry paragraph

### Call Tree-Created Nodes

#### MissingCobolModule
**Description**: Represents a module that is referenced but not found in the codebase.
**Properties**:
- `uuid` (String): Unique identifier
- `module_id` (String): ID/name of the missing module

#### MissingCobolParagraph
**Description**: Represents a paragraph that is referenced but not found in a module.
**Properties**:
- `uuid` (String): Unique identifier
- `paragraph_name` (String): Name of the missing paragraph

#### CobolVariableTarget
**Description**: Represents a variable used as a target in dynamic calls.
**Properties**:
- `uuid` (String): Unique identifier
- `variable_name` (String): Name of the variable used as a call target
- `source_text` (String): Full text of the statement containing the variable
- `usage_context` (String): Context where the variable is used (e.g., "CALL")

#### CicsCommand
**Description**: Represents an EXEC CICS command found in the code.
**Properties**:
- `uuid` (String): Unique identifier
- `command_text` (String): Full text of the CICS command
- `command_type` (String): Type of the CICS command (e.g., "READ", "WRITE")

## Relationship Types

### Parser-Created Relationships

#### CONTAINS
**Description**: Basic structural relationship between program elements.
**Properties**: None - standard source/target connections
**Connects**:
- Module → Division
- Division → Section
- Section → Paragraph

### Call Tree-Created Relationships

#### CALLS
**Description**: Represents a CALL statement from one paragraph to another program or module.
**Properties**:
- `uuid` (String): Unique identifier
- `call_text` (String): The full text of the CALL statement
- `is_dynamic` (Boolean): Flag indicating if this is a dynamic CALL
**Connects**:
- Paragraph → Module/Paragraph
- Paragraph → MissingCobolModule

#### MODULE_CALLS
**Description**: High-level call relationship between modules.
**Properties**:
- `uuid` (String): Unique identifier
**Connects**:
- Module → Module
- Module → MissingCobolModule

#### DYNAMIC_CALLS
**Description**: Represents a CALL to a variable target (dynamic call).
**Properties**:
- `uuid` (String): Unique identifier
**Connects**:
- Paragraph → CobolVariableTarget

#### GOTO
**Description**: Represents a GOTO statement between paragraphs.
**Properties**:
- `uuid` (String): Unique identifier
- `goto_text` (String): The full text of the GOTO statement
**Connects**:
- Paragraph → Paragraph
- Paragraph → MissingCobolParagraph

#### PERFORMS
**Description**: Represents a PERFORM statement between paragraphs or sections.
**Properties**:
- `uuid` (String): Unique identifier
- `perform_text` (String): The full text of the PERFORM statement
- `is_conditional` (Boolean): Flag indicating if this is a conditional PERFORM
- `condition_type` (String, optional): Type of condition (TIMES, UNTIL, VARYING)
- `condition_text` (String, optional): The text of the condition
- `iteration_count` (String, optional): For TIMES conditions, the value or variable name
- `loop_variable` (String, optional): For VARYING conditions, the variable being varied
- `start_value` (String, optional): For VARYING conditions, the starting value
- `increment_value` (String, optional): For VARYING conditions, the increment value
- `loop_structure` (JSON, optional): For nested VARYING conditions, representation of loop structure
**Connects**:
- Paragraph → Paragraph/Section
- Paragraph → MissingCobolParagraph

#### CICS
**Description**: Connects a paragraph to a CICS command.
**Properties**:
- `uuid` (String): Unique identifier
**Connects**:
- Paragraph → CicsCommand

## Workflow and Data Creation

### 1. Parser Process (`cobol_parser_cli.py`)

The parser process is responsible for:
- Processing COBOL source files and their copybooks
- Building the structural representation of COBOL programs
- Creating and storing these nodes and relationships in Neo4j:
  - CobolModule
  - CobolIdentificationDivision
  - CobolEnvironmentDivision
  - CobolDataDivision
  - CobolProcedureDivision
  - CobolSection
  - CobolParagraph
  - CobolEntryParagraph
  - CONTAINS relationships

### 2. Call Tree Analysis Process (`call_tree.py`)

The call tree process is responsible for:
- Analyzing the structural representation built by the parser
- Identifying control flow statements (CALL, GOTO, PERFORM, CICS)
- Creating nodes for missing references and variables
- Establishing relationships that represent execution flow
- Creating and storing these nodes and relationships in Neo4j:
  - MissingCobolModule
  - MissingCobolParagraph
  - CobolVariableTarget
  - CicsCommand
  - CALLS relationships
  - MODULE_CALLS relationships
  - DYNAMIC_CALLS relationships
  - GOTO relationships
  - PERFORMS relationships
  - CICS relationships

## Query Examples

### Finding all modules called by a specific module

```cypher
MATCH (source:CobolModule)-[:MODULE_CALLS]->(target)
WHERE source.module_id = 'MODULE_NAME'
RETURN target.module_id
```

### Finding all paragraphs that perform a specific paragraph

```cypher
MATCH (source:CobolParagraph)-[r:PERFORMS]->(target:CobolParagraph)
WHERE target.paragraph_name = 'PARAGRAPH_NAME'
RETURN source.paragraph_name, r.perform_text
```

### Finding all missing module references

```cypher
MATCH (m:MissingCobolModule)<-[r:CALLS]-(source:CobolParagraph)
RETURN m.module_id, count(r) as reference_count
ORDER BY reference_count DESC
```

### Finding paragraphs with the most outgoing calls

```cypher
MATCH (p:CobolParagraph)-[r:CALLS]->()
RETURN p.paragraph_name, p.module_id, count(r) as call_count
ORDER BY call_count DESC
LIMIT 10
```

### Finding potential unreachable paragraphs

```cypher
MATCH (p:CobolParagraph)
WHERE NOT (p)<-[:CALLS|PERFORMS|GOTO]-()
  AND NOT p:CobolEntryParagraph
RETURN p.paragraph_name, p.module_id
```

## Neo4j Constraints and Indexes

The data model uses these constraints and indexes for optimized performance:

### Unique Constraints

- `CobolModule.uuid`
- `CobolIdentificationDivision.uuid`
- `CobolEnvironmentDivision.uuid`
- `CobolDataDivision.uuid`
- `CobolProcedureDivision.uuid`
- `CobolSection.uuid`
- `CobolParagraph.uuid`
- `CobolEntryParagraph.uuid`
- `MissingCobolModule.uuid`
- `MissingCobolParagraph.uuid`
- `CobolVariableTarget.uuid`
- `CicsCommand.uuid`

### Indexes

- `cobol_module_module_id`
- `cobol_identification_division_program_id`
- `cobol_section_section_name`
- `cobol_paragraph_paragraph_name`
- `cobol_entry_paragraph_entry_name`
- `missing_cobol_module_id`
- `missing_cobol_paragraph_name`
- `cobol_variable_target_name`
- `cics_command_type`

## Visualization Guide

The Neo4j data model can be visualized in Neo4j Browser or Neo4j Bloom with these recommended settings:

### Module-Level View

```cypher
MATCH (m:CobolModule)-[r:MODULE_CALLS]->(m2)
RETURN m, r, m2
LIMIT 100
```

### Paragraph-Level View (for a specific module)

```cypher
MATCH (p:CobolParagraph)-[r]->(target)
WHERE p.module_id = 'MODULE_NAME'
RETURN p, r, target
LIMIT 100
```

### Call Chain View (tracing execution path)

```cypher
MATCH path = (start:CobolEntryParagraph {module_id: 'MODULE_NAME'})-[:CALLS|PERFORMS*1..5]->(target)
RETURN path
LIMIT 50
```

## Known Limitations

1. The analyzer does not attempt to resolve dynamic CALL targets
2. Some complex PERFORM statements with multiple conditions might not be fully parsed
3. The tool does not track inter-module GOTO references
4. Inline PERFORM statements (PERFORM UNTIL without a target paragraph) create complex scenarios requiring special handling