# COBOL Parser System Specification

## Overview
This document specifies a Python 3.11 program designed to parse IBM ENTERPRISE COBOL source code, build an internal representation of the COBOL module structure, and store the parsed data both in an intermediate representation and in a Neo4j database.

## System Requirements

### Programming Language
- Python 3.11

### Dependency Management
- Pipenv for dependency management
- Required dependencies:
  - `py2neo` for Neo4j integration
  - `lark` or `antlr4-python3-runtime` for parsing
  - `click` for command-line interface
  - `pyyaml` for configuration
  - `logging` for system logging

## System Architecture

### Components

1. **COBOL Preprocessor**
   - Handles copybook expansion
   - Manages file inclusion
   - Performs initial text normalization

2. **COBOL Parser**
   - Implements grammar rules for IBM ENTERPRISE COBOL
   - Builds an abstract syntax tree (AST)
   - Identifies and classifies COBOL structural elements

3. **Intermediate Representation Builder**
   - Converts AST to a canonical intermediate representation
   - Implements metadata extraction
   - Manages text preservation rules

4. **Neo4j Connector**
   - Creates graph nodes and relationships
   - Implements batch operations for performance
   - Manages database transactions

5. **Orchestrator**
   - Coordinates processing flow
   - Implements error handling
   - Provides reporting capabilities

### Data Flow

1. COBOL source files → Preprocessor → Expanded source
2. Expanded source → Parser → Abstract Syntax Tree
3. AST → IR Builder → Intermediate Representation
4. Intermediate Representation → Neo4j Connector → Neo4j Database

## Detailed Specifications

### COBOL Module Structure
The parser will recognize the following hierarchical structure:

```
Module (CobolModule)
├── Identification Division (CobolIdentificationDivision)
├── Environment Division (CobolEnvironmentDivision)
├── Data Division (CobolDataDivision)
└── Procedure Division (CobolProcedureDivision)
    └── Sections (CobolSection)
        └── Paragraphs (CobolParagraph or CobolEntryParagraph)
```

### Node Types and Properties

#### Universal Properties (All Nodes)
- **uuid**: A universally unique identifier (UUID) generated during parsing
  - Format should follow RFC 4122 (UUID version 4)
  - Required for all node types

#### Universal Properties (All Relationships)
- **uuid**: A universally unique identifier (UUID) generated during parsing
  - Format should follow RFC 4122 (UUID version 4)
  - Required for all relationship types

#### CobolModule
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Unique identifier for the module
  - `file_name`: Original source file name
  - `full_text`: Complete text of the module including comments

#### CobolIdentificationDivision
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `full_text`: Complete text of the division
  - `program_id`: Value of PROGRAM-ID

#### CobolEnvironmentDivision
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `full_text`: Complete text of the division

#### CobolDataDivision
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `full_text`: Complete text of the division

#### CobolProcedureDivision
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `full_text`: Complete text of the division

#### CobolSection
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `section_name`: Name of the section or "UNNAMED_{module_id}_{sequence_number}" for unnamed sections
  - `full_text`: Complete text of the section including all paragraphs
  - `parent_name`: "PROCEDURE DIVISION"
  - `is_unnamed`: Boolean flag (true for unnamed sections)

#### CobolParagraph
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `paragraph_name`: Name of the paragraph or "UNNAMED_{module_id}_{sequence_number}" for unnamed paragraphs
  - `full_text`: Complete text of the paragraph
  - `parent_name`: Name of the parent section
  - `is_unnamed`: Boolean flag (true for unnamed paragraphs)

#### CobolEntryParagraph
- **Properties**:
  - `uuid`: Universal unique identifier
  - `module_id`: Parent module identifier
  - `paragraph_name`: Name of the paragraph or "UNNAMED_ENTRY_{module_id}" for unnamed entry paragraphs
  - `entry_name`: For the implied entry point (first paragraph of first section), the value of PROGRAM-ID; for explicit entry points, the name of the paragraph
  - `full_text`: Complete text of the paragraph
  - `parent_name`: Name of the parent section
  - `is_main_entry`: Boolean flag indicating if this is the main program entry point
  - `is_unnamed`: Boolean flag (true for unnamed paragraphs)

### Relationships

#### INCLUDES
- **Properties**:
  - `uuid`: Universal unique identifier
- Connects parent nodes to their direct children
- Only spans one level in the hierarchy
- Example relationships:
  - (CobolModule)-[INCLUDES]->(CobolIdentificationDivision)
  - (CobolModule)-[INCLUDES]->(CobolEnvironmentDivision)
  - (CobolModule)-[INCLUDES]->(CobolDataDivision)
  - (CobolModule)-[INCLUDES]->(CobolProcedureDivision)
  - (CobolProcedureDivision)-[INCLUDES]->(CobolSection)
  - (CobolSection)-[INCLUDES]->(CobolParagraph)
  - (CobolSection)-[INCLUDES]->(CobolEntryParagraph)

## Processing Rules

### Copybook Expansion
1. Copybooks must be expanded before parsing begins
2. Copybooks can be located in a different folder from the main source
3. Recursive copybook expansion must be supported (copybooks using other copybooks)
4. The expansion process must preserve line numbers for debugging purposes

### Comment Handling
1. Comments (lines starting with "      *") are excluded from structural parsing
2. Comments are preserved in the `full_text` property of the containing node
3. Comments do not influence the structural hierarchy

### Unnamed Elements Handling
1. Instead of creating separate node types for unnamed elements, use naming conventions:
   - For unnamed sections: "UNNAMED_{module_id}_{sequence_number}"
   - For unnamed paragraphs: "UNNAMED_{module_id}_{sequence_number}"
   - For unnamed entry paragraphs: "UNNAMED_ENTRY_{module_id}"
2. Add an `is_unnamed` boolean property to indicate elements without explicit names
3. This approach simplifies the schema while preserving the information about unnamed elements

### Entry Paragraphs
1. The first paragraph of the first section in the Procedure Division is automatically an entry point:
   - Create a `CobolEntryParagraph` node instead of a `CobolParagraph` node
   - Set `entry_name` to the value from the PROGRAM-ID in the Identification Division
   - Set `is_main_entry` to true
2. Additional paragraphs explicitly marked as entry points:
   - Create a `CobolEntryParagraph` node
   - Set `entry_name` to the paragraph name
   - Set `is_main_entry` to false
3. For unnamed entry paragraphs (first paragraph of first section when unnamed):
   - Create a `CobolEntryParagraph` node with `is_unnamed` set to true
   - Use "UNNAMED_ENTRY_{module_id}" as the `paragraph_name`
   - Set `entry_name` to the value from the PROGRAM-ID
   - Set `is_main_entry` to true
4. The entry point classification takes precedence over the unnamed classification

## Implementation Guidelines

### Configuration
- Use YAML configuration for:
  - Neo4j connection details
  - Copybook directories
  - Output paths for intermediate representation
  - Logging settings

### Error Handling
1. Implement robust error handling for:
   - File not found errors
   - Syntax errors in COBOL source
   - Database connection failures
   - Copybook expansion failures
2. Provide detailed error messages with line numbers
3. Generate warning logs for potential issues

### Performance Considerations
1. Implement batch processing for Neo4j operations
2. Consider parallel processing for multiple input files
3. Optimize memory usage for large COBOL programs
4. Implement progress reporting for long-running operations

### Intermediate Representation
1. Store intermediate representation in a structured format (JSON or YAML)
2. Include metadata about the parsing process
3. Preserve the hierarchical structure of the COBOL module
4. Provide sufficient information for downstream processing

## Command-Line Interface

The program should provide a command-line interface with the following options:

```
python cobol_parser.py [OPTIONS] [INPUT_FILES...]

Options:
  --config-file PATH                Path to configuration file
  --copybook-dir DIRECTORY          Directory containing copybooks
  --output-dir DIRECTORY            Directory for intermediate representation
  --neo4j-url TEXT                  Neo4j database URL
  --neo4j-user TEXT                 Neo4j username
  --neo4j-password TEXT             Neo4j password
  --log-level [DEBUG|INFO|WARNING|ERROR]
                                    Set logging level
  --batch-size INTEGER              Number of operations in one Neo4j transaction
  --help                            Show this message and exit
```

## Testing Strategy

1. **Unit Tests**:
   - Parsers for each division
   - Copybook expansion
   - Node creation and relationship building
   - Error handling

2. **Integration Tests**:
   - End-to-end parsing of sample COBOL programs
   - Database interaction
   - Intermediate representation generation

3. **Test Data**:
   - Simple COBOL programs for basic functionality
   - Complex programs with nested structures
   - Programs with various special cases (including unnamed sections and paragraphs)
   - Malformed programs for error handling testing

## Project Structure

```
cobol-parser/
├── Pipfile
├── Pipfile.lock
├── README.md
├── config/
│   └── default.yaml
├── src/
│   ├── __init__.py
│   ├── cli.py                 # Command-line interface
│   ├── config.py              # Configuration handling
│   ├── preprocessor/
│   │   ├── __init__.py
│   │   ├── copybook.py        # Copybook expansion
│   │   └── normalizer.py      # Text normalization
│   ├── parser/
│   │   ├── __init__.py
│   │   ├── grammar.py         # COBOL grammar rules
│   │   ├── ast_builder.py     # Abstract syntax tree construction
│   │   └── visitors/          # AST visitors for different divisions
│   ├── ir/
│   │   ├── __init__.py
│   │   ├── builder.py         # Intermediate representation builder
│   │   └── serializer.py      # IR serialization
│   ├── neo4j/
│   │   ├── __init__.py
│   │   ├── connector.py       # Neo4j database connector
│   │   └── schema.py          # Node and relationship definitions
│   └── orchestrator.py        # Process coordination
├── tests/
│   ├── __init__.py
│   ├── test_preprocessor.py
│   ├── test_parser.py
│   ├── test_ir.py
│   ├── test_neo4j.py
│   ├── test_orchestrator.py
│   └── fixtures/              # Test COBOL programs
└── docs/
    ├── architecture.md
    ├── usage.md
    └── api.md
```

## Edge Cases and Special Considerations

### Unnamed First Section and First Paragraph
When the first section and its first paragraph in the Procedure Division are both unnamed:

1. Create a `CobolSection` with:
   - `section_name`: "UNNAMED_SECTION"
   - `is_unnamed`: true

2. Create a `CobolEntryParagraph` with:
   - `paragraph_name`: "UNNAMED_ENTRY"
   - `entry_name`: Value from PROGRAM-ID
   - `parent_name`: "UNNAMED_SECTION"
   - `is_main_entry`: true
   - `is_unnamed`: true

Example COBOL code for this edge case:
```cobol
PROCEDURE DIVISION.
    DISPLAY 'START OF EXECUTION OF PROGRAM CBACT01C'.
    PERFORM 0000-ACCTFILE-OPEN.

    PERFORM UNTIL END-OF-FILE = 'Y'
        IF  END-OF-FILE = 'N'
            PERFORM 1000-ACCTFILE-GET-NEXT
            IF  END-OF-FILE = 'N'
                DISPLAY ACCOUNT-RECORD
            END-IF
        END-IF
    END-PERFORM.
```

This specification provides a comprehensive framework for implementing a COBOL parser that meets the stated requirements. The actual implementation should follow these guidelines while allowing for adjustments based on practical considerations encountered during development.