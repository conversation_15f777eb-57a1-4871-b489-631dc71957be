Below is the example cobol program

````COBOL
IDENTIFICATION DIVISION.
PROGRAM-ID. SYSTEM-INFO.

ENVIRONMENT DIVISION.
CONFIGURATION SECTION.

DATA DIVISION.
WORKING-STORAGE SECTION.
01 WS-SYSTEM-DATE PIC X(8).
01 WS-SYSTEM-TIME PIC X(8).
01 WS-USERNAME    PIC X(20).

PROCEDURE DIVISION.
MAIN-PARA.
    PERFORM GET-DATE.
    PERFORM GET-TIME.
    PERFORM GET-USERNAME.
    PERFORM DISPLAY-INFO.
    STOP RUN.

* Retrieves the current system date
GET-DATE.
    MOVE FUNCTION CURRENT-DATE(1:8) TO WS-SYSTEM-DATE.

* Retrieves the current system time
GET-TIME.
    MOVE FUNCTION CURRENT-DATE(9:6) TO WS-SYSTEM-TIME.
* Additional userInfo
* Retrieves the username of the current user
GET-<PERSON><PERSON><PERSON><PERSON>.
    MOVE FUNCTION USER-ID TO WS-<PERSON><PERSON>NA<PERSON>.

* Displays the collected system information
DISPLAY-INFO.
    DISPLAY "System Information:".
    DISPLAY "Date      : " WS-SYSTEM-DATE.
    DISPLAY "Time      : " WS-SYSTEM-TIME.
    DISPLAY "User Name : " WS-USERNAME.
````

Its PROCEDURE DIVISION should be splitted into the following elements:

1. `CobolSection` with:
   - `section_name`: "UNNAMED_SECTION"
   - ...
2. a `CobolEntryParagraph` with:
   - `paragraph_name`: "MAIN"
   - `full_text`:
     ````
        MAIN-PARA.
        PERFORM GET-DATE.
        PERFORM GET-TIME.
        PERFORM GET-USERNAME.
        PERFORM DISPLAY-INFO.
        STOP RUN.
     ````
   - ...
3. a `CobolParagraph` with:
   - `paragraph_name`: "GET-DATE"
   - `full_text`:
     ````
        * Retrieves the current system date
        GET-DATE.
            MOVE FUNCTION CURRENT-DATE(1:8) TO WS-SYSTEM-DATE.
     ````
   - ...

4. a `CobolParagraph` with:
   - `paragraph_name`: "GET-TIME"
   - `full_text`:
     ````
        * Retrieves the current system time
        GET-TIME.
            MOVE FUNCTION CURRENT-DATE(9:6) TO WS-SYSTEM-TIME.
     ````
   - ...

5. a `CobolParagraph` with:
   - `paragraph_name`: "GET-USERNAME"
   - `full_text`:
     ````
        * Additional userInfo
        * Retrieves the username of the current user
        GET-USERNAME.
            MOVE FUNCTION USER-ID TO WS-USERNAME.
     ````
   - ...

6. a `CobolParagraph` with:
   - `paragraph_name`: "DISPLAY-INFO"
   - `full_text`:
     ````
        * Displays the collected system information
        DISPLAY-INFO.
            DISPLAY "System Information:".
            DISPLAY "Date      : " WS-SYSTEM-DATE.
            DISPLAY "Time      : " WS-SYSTEM-TIME.
            DISPLAY "User Name : " WS-USERNAME.
     ````
   - ...

