# COBOL Call Tree Enhancement Specification

## Overview

This specification outlines the implementation of a new standalone CLI tool that builds a call tree representation in Neo4j by analyzing COBOL source code. The tool will analyze COBOL control flow statements such as CALL, GOTO, PERFORM, and EXEC CICS to create appropriate nodes and relationships in Neo4j.

## Implementation Requirements

1. Create a separate CLI tool that solely focuses on the call tree analysis
2. Use Neo4j as the only source of truth (no local/intermediate storage)
3. Implement support for CALL, GOTO, PERFORM, and EXEC CICS statements
4. Build the implementation in phases by relationship type

## Neo4j Schema

### Node Types

1. **MissingCobolModule**
   - `uuid`: Universal unique identifier
   - `module_id`: The name of the missing module

2. **MissingCobolParagraph**
   - `uuid`: Universal unique identifier
   - `paragraph_name`: The name of the missing paragraph

3. **CobolVariableTarget**
   - `uuid`: Universal unique identifier
   - `variable_name`: The name of the variable containing the target
   - `source_text`: The full text of the statement containing the variable target
   - `usage_context`: The context where this variable target is used (e.g., "CALL", "PERFORM")

4. **CicsCommand**
   - `uuid`: Universal unique identifier
   - `command_text`: The full text of the CICS command
   - `command_type`: The type of CICS command (e.g., READ, WRITE, SEND)

### Relationship Types

1. **CALLS**
   - Connects a source paragraph to a target entry paragraph, missing module, or variable target
   - `uuid`: Universal unique identifier
   - `call_text`: The full text of the CALL statement
   - `is_dynamic`: Boolean flag indicating if this is a dynamic CALL

2. **MODULE_CALLS**
   - Connects a source module to a target module or missing module
   - `uuid`: Universal unique identifier

3. **DYNAMIC_CALLS**
   - Connects a source module to a variable target
   - `uuid`: Universal unique identifier

4. **GOTO**
   - Connects a source paragraph to a target paragraph
   - `uuid`: Universal unique identifier
   - `goto_text`: The full text of the GOTO statement

5. **PERFORMS**
   - Connects a source paragraph to a target paragraph or section
   - `uuid`: Universal unique identifier
   - `perform_text`: The full text of the PERFORM statement
   - `is_conditional`: Boolean flag indicating if this is a conditional PERFORM
   - `condition_type`: Type of condition (TIMES, UNTIL, VARYING, VARYING_NESTED)
   - `condition_text`: The text of the condition
   - `iteration_count`: For TIMES conditions, the value or variable name
   - `loop_variable`: For VARYING conditions, the variable being varied
   - `start_value`: For VARYING conditions, the starting value
   - `increment_value`: For VARYING conditions, the increment value
   - `loop_structure`: For nested VARYING conditions, JSON representation of loop structure

6. **CICS**
   - Connects a paragraph to a CICS command
   - `uuid`: Universal unique identifier

## Phased Implementation Plan

### Phase 1: Core CLI and Static CALL Analysis
- Setup the basic CLI framework
- Reuse existing Neo4j connection handling from src/neo4j/connector.py
- Process static CALL statements (including CALL ... USING ...) and create CALLS relationships
- Create MODULE_CALLS relationships between modules

### Phase 2: Dynamic CALL Analysis
- Process dynamic CALL statements with variable targets
- Create CobolVariableTarget nodes
- Create DYNAMIC_CALLS relationships

### Phase 3: GOTO Analysis
- Process GOTO statements
- Create GOTO relationships between paragraphs
- Implement line number resolution for GOTO targets

### Phase 4: Basic PERFORM Analysis
- Process simple PERFORM statements with paragraph targets
- Exclude inline PERFORM statements (like "PERFORM UNTIL...")
- Create PERFORMS relationships between paragraphs
- Handle missing paragraph targets

### Phase 5: Advanced PERFORM Analysis
- Process conditional PERFORM statements (TIMES, UNTIL, VARYING)
- Process PERFORM THROUGH/THRU statement ranges
- Handle section targets in PERFORM statements

### Phase 6: EXEC CICS Analysis
- Process EXEC CICS statements
- Create CicsCommand nodes
- Create CICS relationships

## Command-Line Interface

```
python cobol_call_tree.py [OPTIONS]

Options:
  --analysis-type TEXT      Type of analysis to perform (all, calls, goto, perform, cics)
  --batch-size INTEGER      Number of statements to process in each transaction
  --report-missing          Generate report of missing targets
  --report-file TEXT        Path to save the missing targets report
  --module-filter TEXT      Optional filter to process only specific modules (supports wildcards)
```

Note: Neo4j connection parameters and logging level will be used from the existing configuration file.

## Development Guidelines

1. **Neo4j Integration**:
   - Reuse existing Neo4j connection implementation from src/neo4j/connector.py
   - Extend the schema in src/neo4j/schema.py as needed
   - Implement efficient batched transactions for performance
   - Ensure proper index usage for node lookups

2. **COBOL Processing**:
   - Work exclusively with Neo4j as the only source of truth
   - Query existing CobolModule and CobolParagraph nodes from Neo4j, including their source text
   - Analyze the paragraph text stored in Neo4j to identify control flow statements
   - Use input file names only to reference module names for proper queries
   - Extract statement details using regular expressions or a lightweight parser on the paragraph text from Neo4j
   - Ensure proper handling of CALL statements with USING clause

3. **PERFORM Statement Processing**:
   - Clearly distinguish between PERFORM statements targeting paragraphs and inline PERFORM statements
   - Only create PERFORMS relationships for PERFORM statements targeting named paragraphs
   - Ignore inline PERFORM statements (e.g., "PERFORM UNTIL...", "PERFORM VARYING...")
   - Properly handle PERFORM THROUGH/THRU statements

4. **Error Handling**:
   - Log all parsing and processing errors
   - Create MissingCobolModule and MissingCobolParagraph nodes when targets don't exist
   - Implement robust recovery when encountering malformed statements

5. **Testing**:
   - Build unit tests for each statement type processor
   - Create integration tests with Neo4j
   - Generate test coverage reports

## Feature Acceptance (Gherkin Format)

```gherkin
Feature: COBOL Call Tree Analysis

  Scenario: Analyze Static CALL Statements
    Given a COBOL program with static CALL statements
    When the call tree analyzer is run with the "calls" analysis type
    Then CALLS relationships should be created between source and target paragraphs
    And MODULE_CALLS relationships should be created between modules

  Scenario: Handle Missing Module References
    Given a COBOL program that calls a non-existent module
    When the call tree analyzer processes the program
    Then a MissingCobolModule node should be created
    And CALLS relationships should connect to the missing module node

  Scenario: Process Dynamic CALL Statements
    Given a COBOL program with variable targets in CALL statements
    When the call tree analyzer processes the program
    Then CobolVariableTarget nodes should be created for each variable target
    And DYNAMIC_CALLS relationships should connect modules to variable targets

  Scenario: Analyze GOTO Statements
    Given a COBOL program with GOTO statements
    When the call tree analyzer is run with the "goto" analysis type
    Then GOTO relationships should be created between paragraphs

  Scenario: Process PERFORM Statements
    Given a COBOL program with PERFORM statements
    When the call tree analyzer is run with the "perform" analysis type
    Then PERFORMS relationships should be created between paragraphs

  Scenario: Handle Conditional PERFORM Statements
    Given a COBOL program with conditional PERFORM statements
    When the call tree analyzer processes the program
    Then PERFORMS relationships should have conditional properties set correctly

  Scenario: Process PERFORM THROUGH Ranges
    Given a COBOL program with PERFORM THROUGH statements
    When the call tree analyzer processes the program
    Then PERFORMS relationships should connect all paragraphs in the range

  Scenario: Analyze EXEC CICS Commands
    Given a COBOL program with EXEC CICS statements
    When the call tree analyzer is run with the "cics" analysis type
    Then CicsCommand nodes should be created for each CICS command
    And CICS relationships should connect paragraphs to commands
```

## Project Structure

```
cobol-parser/
├── cobol_call_tree.py           # Main CLI entry point
├── src/
│   ├── call_tree/
│   │   ├── __init__.py
│   │   ├── analyzer/
│   │   │   ├── __init__.py
│   │   │   ├── base.py          # Base analyzer class
│   │   │   ├── call_analyzer.py # CALL statement analyzer
│   │   │   ├── goto_analyzer.py # GOTO statement analyzer
│   │   │   ├── perform_analyzer.py # PERFORM statement analyzer
│   │   │   └── cics_analyzer.py # EXEC CICS analyzer
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── nodes.py         # Node type definitions
│   │   │   └── relationships.py # Relationship type definitions
│   │   └── utils/
│   │       ├── __init__.py
│   │       └── parser.py        # COBOL parsing utilities
│   ├── neo4j/
│   │   ├── connector.py         # Existing Neo4j connection handling
│   │   └── schema.py            # Existing Neo4j schema definitions
└── tests/
    ├── call_tree/
    │   ├── __init__.py
    │   ├── test_call_analyzer.py
    │   ├── test_goto_analyzer.py
    │   ├── test_perform_analyzer.py
    │   └── test_cics_analyzer.py
```