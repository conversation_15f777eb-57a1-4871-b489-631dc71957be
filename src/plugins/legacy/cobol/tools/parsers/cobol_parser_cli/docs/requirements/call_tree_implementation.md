# COBOL Call Tree Implementation Documentation

## Overview

This document describes the implementation of the COBOL call tree analysis functionality as specified in the "calltree_spec.md" requirement. The call tree analysis tool analyzes COBOL source code to identify control flow statements (CALL, GOTO, PERFORM, EXEC CICS) and creates appropriate nodes and relationships in a Neo4j graph database.

## Implementation Structure

The call tree analyzer has been implemented with the following components:

1. **Command-line Interface**
   - `call_tree.py` - Main entry point for the call tree analyzer

2. **Core Functionality**
   - `src/call_tree/orchestrator.py` - Coordinates the analysis process
   - `src/call_tree/analyzer/base.py` - Base class for all analyzers
   - `src/call_tree/analyzer/call_analyzer.py` - Analyzes CALL statements
   - `src/call_tree/analyzer/goto_analyzer.py` - Analyzes GOTO statements
   - `src/call_tree/analyzer/perform_analyzer.py` - Analyzes PERFORM statements
   - `src/call_tree/analyzer/cics_analyzer.py` - Analyzes EXEC CICS statements

3. **Models**
   - `src/call_tree/models/nodes.py` - Node type definitions
   - `src/call_tree/models/relationships.py` - Relationship type definitions

4. **Utilities**
   - `src/call_tree/utils/parser.py` - COBOL statement parsing utilities

5. **Neo4j Integration**
   - `src/neo4j/call_tree_schema.py` - Schema definitions for Neo4j

## Supported Analysis Types

The tool supports the following analysis types:

1. **CALL Analysis**
   - Static CALL statements (e.g., `CALL "MODULE1"`)
   - Dynamic CALL statements (e.g., `CALL PROGRAM-NAME`)
   - Creating `CALLS` and `MODULE_CALLS` relationships
   - Identifying missing module references

2. **GOTO Analysis**
   - GOTO statements (e.g., `GO TO PARAGRAPH1`)
   - Creating `GOTO` relationships
   - Identifying missing paragraph references

3. **PERFORM Analysis**
   - Basic PERFORM statements (e.g., `PERFORM PARAGRAPH1`)
   - PERFORM THROUGH statements (e.g., `PERFORM PARA1 THRU PARA3`)
   - Conditional PERFORM statements (TIMES, UNTIL, VARYING)
   - Creating `PERFORMS` relationships
   - Identifying missing paragraph references

4. **CICS Analysis**
   - EXEC CICS statements (e.g., `EXEC CICS READ...END-EXEC`)
   - Creating `CicsCommand` nodes and `CICS` relationships

## Neo4j Schema

The following Neo4j nodes and relationships have been added:

### Nodes

1. **MissingCobolModule**
   - `uuid`: Universal unique identifier
   - `module_id`: The name of the missing module

2. **MissingCobolParagraph**
   - `uuid`: Universal unique identifier
   - `paragraph_name`: The name of the missing paragraph

3. **CobolVariableTarget**
   - `uuid`: Universal unique identifier
   - `variable_name`: The name of the variable containing the target
   - `source_text`: The full text of the statement containing the variable target
   - `usage_context`: The context where this variable target is used (e.g., "CALL")

4. **CicsCommand**
   - `uuid`: Universal unique identifier
   - `command_text`: The full text of the CICS command
   - `command_type`: The type of CICS command (e.g., READ, WRITE, SEND)

### Relationships

1. **CALLS**
   - Connects a source paragraph to a target entry paragraph, missing module, or variable target
   - `uuid`: Universal unique identifier
   - `call_text`: The full text of the CALL statement
   - `is_dynamic`: Boolean flag indicating if this is a dynamic CALL

2. **MODULE_CALLS**
   - Connects a source module to a target module or missing module
   - `uuid`: Universal unique identifier

3. **DYNAMIC_CALLS**
   - Connects a source module to a variable target
   - `uuid`: Universal unique identifier

4. **GOTO**
   - Connects a source paragraph to a target paragraph
   - `uuid`: Universal unique identifier
   - `goto_text`: The full text of the GOTO statement

5. **PERFORMS**
   - Connects a source paragraph to a target paragraph or section
   - `uuid`: Universal unique identifier
   - `perform_text`: The full text of the PERFORM statement
   - `is_conditional`: Boolean flag indicating if this is a conditional PERFORM
   - `condition_type`: Type of condition (TIMES, UNTIL, VARYING)
   - `condition_text`: The text of the condition
   - Additional properties for specific condition types

6. **CICS**
   - Connects a paragraph to a CICS command
   - `uuid`: Universal unique identifier

## Command-line Usage

The call tree analyzer can be run with the following command:

```bash
python call_tree.py [OPTIONS]
```

Options:
- `--analysis-type` TEXT: Type of analysis to perform (all, calls, goto, perform, cics)
- `--batch-size` INTEGER: Number of statements to process in each transaction
- `--report-missing`: Generate report of missing targets
- `--report-file` TEXT: Path to save the missing targets report
- `--module-filter` TEXT: Optional filter to process only specific modules (supports wildcards)
- `--config` TEXT: Path to custom configuration file

## Missing Targets Report

The call tree analyzer can generate a report of missing targets, which includes:

1. Missing modules referenced by CALL statements
2. Missing paragraphs referenced by GOTO and PERFORM statements
3. Dynamic CALL targets using variables

The report is saved in JSON format and can be used to identify potential issues in the codebase.

## Test Coverage

The implementation includes unit tests covering:

1. Call tree orchestrator functionality
2. CALL statement parsing and analysis
3. GOTO statement parsing and analysis
4. PERFORM statement parsing and analysis
5. CICS statement parsing and analysis
6. Missing targets report generation

## Known Limitations

1. The tool only analyzes paragraphs and sections that already exist in Neo4j (processed by the COBOL parser)
2. Some complex PERFORM statements may not be fully parsed (e.g., nested VARYING conditions)
3. The tool does not attempt to resolve dynamic CALL targets (variables)
4. The tool does not track inter-module GOTO references

## Future Enhancements

1. Resolve dynamic CALL targets through data flow analysis
2. Improve handling of complex PERFORM statements
3. Expand CICS command analysis to identify specific operation types
4. Add visualization capabilities for the call tree
5. Implement runtime metrics tracking for call paths