import logging
from typing import Dict, List, Any, Optional

from src.platform.tools.data.kuzu import KuzuConnector
from ..config import Config
from .analyzer.call_analyzer import CallAnalyzer
from .analyzer.goto_analyzer import GotoAnaly<PERSON>
from .analyzer.perform_analyzer import <PERSON><PERSON><PERSON>nal<PERSON><PERSON>
from .analyzer.cics_analyzer import CicsAnalyzer


class CallTreeOrchestrator:
    """Orchestrator for COBOL call tree analysis"""

    def __init__(self, config: Config):
        """Initialize the orchestrator with configuration

        Args:
            config: The configuration object
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Initialize graph database connector
        ## TODO Now it's <PERSON><PERSON>, but it should be an abstract factory call to
        ##  create a preconfigured type of graph db
        self.graph_db_connector = KuzuConnector()

    def analyze(self, analysis_types: List[str] = None, module_filter: str = None) -> Dict[str, int | str]:
        """Perform call tree analysis

        Args:
            analysis_types: List of analysis types to perform (calls, goto, perform, cics)
            module_filter: Optional filter to process only specific modules

        Returns:
            Dictionary with analysis results
        """
        self.logger.info("Starting call tree analysis")

        # Default to all analysis types if none specified
        if not analysis_types:
            analysis_types = ["calls", "goto", "perform", "cics"]

        # Connect to a graph database
        if not self.graph_db_connector.connect():
            self.logger.error("Failed to connect to graph database database")
            return {"error": "Failed to connect to graph database database"}

        # Create schema
        # self.schema.create_schema(self.graph_db_connector.connection)
        ## TODO Commented as it should be created by the moment of calling this analyze
        ## TODO Check if it is always true

        # Track analysis results
        results: Dict[str, int | str] = {
            "nodes_created": 0,
            "relationships_created": 0
        }

        # Perform requested analyses
        try:
            # CALL analysis
            if "calls" in analysis_types or "all" in analysis_types:
                self.logger.info("Performing CALL analysis")
                call_analyzer = CallAnalyzer(self.graph_db_connector)
                call_nodes, call_relationships = call_analyzer.analyze()
                results["nodes_created"] += call_nodes
                results["relationships_created"] += call_relationships

            # GOTO analysis
            if "goto" in analysis_types or "all" in analysis_types:
                self.logger.info("Performing GOTO analysis")
                goto_analyzer = GotoAnalyzer(self.graph_db_connector)
                goto_nodes, goto_relationships = goto_analyzer.analyze()
                results["nodes_created"] += goto_nodes
                results["relationships_created"] += goto_relationships

            # PERFORM analysis
            if "perform" in analysis_types or "all" in analysis_types:
                self.logger.info("Performing PERFORM analysis")
                perform_analyzer = PerformAnalyzer(self.graph_db_connector)
                perform_nodes, perform_relationships = perform_analyzer.analyze()
                results["nodes_created"] += perform_nodes
                results["relationships_created"] += perform_relationships

            # CICS analysis
            if "cics" in analysis_types or "all" in analysis_types:
                self.logger.info("Performing CICS analysis")
                cics_analyzer = CicsAnalyzer(self.graph_db_connector)
                cics_nodes, cics_relationships = cics_analyzer.analyze()
                results["nodes_created"] += cics_nodes
                results["relationships_created"] += cics_relationships

        except Exception as e:
            self.logger.error(f"Error during analysis: {str(e)}")
            results["error"] = f"Error during analysis: {str(e)}"

        # Disconnect from graph database
        self.graph_db_connector.disconnect()

        self.logger.info(f"Call tree analysis completed: Created {results['nodes_created']} nodes and {results['relationships_created']} relationships")
        return results

    def generate_missing_targets_report(self, output_file: Optional[str] = None) -> Dict[str, Any]:
        """Generate a report of missing targets

        Args:
            output_file: Path to save the report (optional)

        Returns:
            Dictionary with report data
        """
        self.logger.info("Generating missing targets report")

        # Connect to graph database
        if not self.graph_db_connector.connect():
            self.logger.error("Failed to connect to graph database database")
            return {"error": "Failed to connect to graph database database"}

        # Get missing modules
        missing_modules_query = """
        MATCH (m:MissingCobolModule)
        RETURN m.module_id AS module_id, count(*) AS reference_count
        ORDER BY reference_count DESC
        """

        missing_modules = self.graph_db_connector.connection.execute(missing_modules_query)

        # Get missing paragraphs
        missing_paragraphs_query = """
        MATCH (p:MissingCobolParagraph)
        RETURN p.paragraph_name AS paragraph_name, count(*) AS reference_count
        ORDER BY reference_count DESC
        """

        missing_paragraphs = self.graph_db_connector.connection.execute(missing_paragraphs_query)

        # Get dynamic calls
        dynamic_calls_query = """
        MATCH (v:CobolVariableTarget {usage_context: 'CALL'})
        RETURN v.variable_name AS variable_name, count(*) AS usage_count
        ORDER BY usage_count DESC
        """

        dynamic_calls = self.graph_db_connector.connection.execute(dynamic_calls_query)

        # Build report
        report = {
            "missing_modules": missing_modules,
            "missing_paragraphs": missing_paragraphs,
            "dynamic_calls": dynamic_calls
        }

        # Save report to file if requested
        if output_file:
            try:
                import json
                with open(output_file, 'w') as f:
                    json.dump(report, f, indent=2)
                self.logger.info(f"Report saved to {output_file}")
            except Exception as e:
                self.logger.error(f"Failed to save report to {output_file}: {str(e)}")

        # Disconnect from graph database
        self.graph_db_connector.disconnect()

        self.logger.info("Missing targets report generated")
        return report