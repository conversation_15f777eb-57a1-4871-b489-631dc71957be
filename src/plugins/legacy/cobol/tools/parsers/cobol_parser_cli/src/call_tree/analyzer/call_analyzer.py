import re
from typing import Dict, Any, List, Optional, Tuple

from src.platform.tools.data.kuzu import KuzuConnector
from ..models.nodes import MissingCobolModule, CobolVariableTarget
from ..models.relationships import CallsRelationship, ModuleCallsRelationship, DynamicCallsRelationship
from .base import BaseAnalyzer


## TODO Refactoring: we should extract all database operation from here to a Data layer abstract class
##  and implement them behind that class, implementing concrete DB connector classes
class CallAnalyzer(BaseAnalyzer):
    """Analyzer for CALL statements in COBOL code"""

    def __init__(self, graph_db_connector: KuzuConnector):
        """Initialize the CALL statement analyzer

        Args:
            graph_db_connector: graph database connector instance
        """
        super().__init__(graph_db_connector)

        # Regular expressions for CALL statements
        # Static CALL
        self.static_call_pattern = re.compile(
            r'(?i)CALL\s+[\'"]([^\'"]*)[\'"]\s*(?:USING\s+(.*?))?(?:\s+END-CALL|\s*\.)'
        )

        # Dynamic CALL with identifier
        self.dynamic_call_pattern = re.compile(
            r'(?i)CALL\s+([A-Za-z0-9_-]+)\s*(?:USING\s+(.*?))?(?:\s+END-CALL|\s*\.)'
        )

    def analyze(self) -> Tuple[int, int] | None:
        """Analyze CALL statements in COBOL code

        Returns:
            Tuple of (nodes_created, relationships_created)
        """
        self.logger.info("Starting CALL statement analysis")

        try:
            # Get all modules
            modules = self._get_all_modules()
            self.logger.info(f"Found {len(modules)} modules to analyze")

            nodes_created = 0
            relationships_created = 0

            # Process each module
            for module in modules:
                module_id = module["module_id"]
                module_uuid = module["uuid"]

                self.logger.info(f"Processing module: {module_id}")

                # Get all paragraphs in the module
                paragraphs = self._find_paragraphs_in_module(module_id)
                self.logger.info(f"Found {len(paragraphs)} paragraphs in module {module_id}")

                # Process each paragraph
                for paragraph in paragraphs:

                    if not paragraph['full_text']:
                        self.logger.warning(f"Paragraph {paragraph['name']} in {module_id} has no text")
                        continue

                    # Process static CALLS
                    static_calls = self._find_static_calls(paragraph['full_text'])
                    for call in static_calls:
                        called_module_name = call["target"]
                        call_text = call["text"]

                        # Find the called module
                        called_module = self._find_module_by_id(called_module_name)

                        # If the called module doesn't exist, create a missing module node
                        if not called_module:
                            missing_module = MissingCobolModule(called_module_name)
                            missing_module_dict = missing_module.to_dict()
                            missing_module_uuid = self._create_node(missing_module_dict)

                            if missing_module_uuid:
                                called_module = {"uuid": missing_module_uuid, "module_id": called_module_name}
                                nodes_created += 1

                        # If we have a called module (either found or created), create the relationships
                        if called_module:
                            # Create CALLS relationship (paragraph to entry paragraph or missing module)
                            # Try to find the entry paragraph of the called module
                            entry_para = self._find_entry_paragraph(called_module["module_id"])

                            if entry_para:
                                # Create CALLS relationship to entry paragraph
                                calls_rel = CallsRelationship(
                                    from_uuid=paragraph["uuid"],
                                    from_type=paragraph["type_label"],
                                    to_uuid=entry_para["uuid"],
                                    to_type="CobolEntryParagraph",
                                    call_text=call_text,
                                    is_dynamic=False
                                )
                                calls_rel_dict = calls_rel.to_dict()
                                if self._create_relationship(calls_rel_dict):
                                    relationships_created += 1
                            else:
                                # Create a CALLS relationship to module
                                calls_rel = CallsRelationship(
                                    from_uuid=paragraph["uuid"],
                                    from_type=paragraph["type_label"],
                                    to_uuid=called_module["uuid"],
                                    to_type="MissingCobolModule",
                                    call_text=call_text,
                                    is_dynamic=False
                                )
                                calls_rel_dict = calls_rel.to_dict()
                                if self._create_relationship(calls_rel_dict):
                                    relationships_created += 1

                            # Create MODULE_CALLS relationship (module to module)
                            module_calls_rel = ModuleCallsRelationship(
                                from_uuid=module_uuid,
                                from_type="CobolModule",
                                to_uuid=called_module["uuid"],
                                to_type="MissingCobolModule"
                            )
                            module_calls_rel_dict = module_calls_rel.to_dict()
                            if self._create_relationship(module_calls_rel_dict):
                                relationships_created += 1

                    # Process dynamic CALLS
                    dynamic_calls = self._find_dynamic_calls(paragraph['full_text'])
                    for call in dynamic_calls:
                        variable_name = call["target"]
                        call_text = call["text"]

                        # Create a CobolVariableTarget node
                        variable_target = CobolVariableTarget(
                            variable_name=variable_name, ## TODO Maybe we will need, maybe not, -> module_id=module_id,
                            source_text=call_text,
                            usage_context="CALL"
                        )
                        variable_target_dict = variable_target.to_dict()
                        variable_target_uuid = self._create_node(variable_target_dict)

                        if variable_target_uuid:
                            nodes_created += 1

                            # Create a CALLS relationship (paragraph to variable target)
                            calls_rel = CallsRelationship(
                                from_uuid=paragraph["uuid"],
                                from_type=paragraph["type_label"],
                                to_uuid=variable_target_uuid,
                                to_type="CobolVariableTarget",
                                call_text=call_text,
                                is_dynamic=True
                            )
                            calls_rel_dict = calls_rel.to_dict()
                            if self._create_relationship(calls_rel_dict):
                                relationships_created += 1

                            # Create a DYNAMIC_CALLS relationship (module to variable target)
                            dynamic_calls_rel = DynamicCallsRelationship(
                                from_uuid=module_uuid,
                                from_type="CobolModule",
                                to_uuid=variable_target_uuid,
                                to_type="CobolVariableTarget"
                            )
                            dynamic_calls_rel_dict = dynamic_calls_rel.to_dict()
                            if self._create_relationship(dynamic_calls_rel_dict):
                                relationships_created += 1

            self.logger.info(
                f"CALL analysis completed: Created {nodes_created} nodes and {relationships_created} relationships")
            return nodes_created, relationships_created
        except Exception as e:
            self.logger.error(f"Call analyzer's analyze() failed {str(e)}")
            return None

    def _get_all_modules(self) -> List[Dict[str, Any]]:
        """Get all COBOL modules in the database

        Returns:
            List of modules
        """
        try:
            # Execute the query and convert the result to a list of dictionaries
            query = """
            MATCH (m:CobolModule)
            RETURN m.uuid AS uuid, m.module_id AS module_id
            """
            response = self.graph_db_connector.connection.execute(query)
            nodes = []
            while response.has_next():
                uuid, module_id = response.get_next()
                nodes.append({'uuid': uuid, 'module_id': module_id})
            return nodes
        except Exception as e:
            self.logger.error(f"self._get_all_modules(): Error executing Cypher query: {str(e)}")
            return []

    def _find_entry_paragraph(self, module_id: str) -> Optional[Dict[str, Any]] | None:
        """Find the entry paragraph of a module

        Args:
            module_id: The module ID

        Returns:
            Entry paragraph data or None if not found
        """
        # Look for entry paragraphs first
        try:
            query = """
            MATCH (p:CobolEntryParagraph)
            WHERE p.module_id = $module_id
            RETURN p.uuid AS uuid, p.paragraph_name AS paragraph_name
            LIMIT 1
            """

            response = self.graph_db_connector.connection.execute(query, {"module_id": module_id})
            nodes = []
            while response.has_next():
                uuid, paragraph_name = response.get_next()
                nodes.append({'uuid': uuid, 'paragraph_name': paragraph_name})

            if nodes:
                return nodes[0]

            # If no entry paragraph found, find the first paragraph
            query = """
            MATCH (p:CobolParagraph) 
            WHERE p.module_id = $module_id 
            RETURN p.uuid AS uuid, p.paragraph_name AS paragraph_name ORDER BY p.paragraph_name 
            LIMIT 1;
            """

            response = self.graph_db_connector.connection.execute(query, {"module_id": module_id})
            nodes = []
            while response.has_next():
                uuid, paragraph_name = response.get_next()
                nodes.append({'uuid': uuid, 'paragraph_name': paragraph_name})

            return nodes[0] if nodes else None

        except Exception as e:
            self.logger.error(f"self._get_all_modules(): Error executing Cypher query: {str(e)}")
            return None

    def _find_static_calls(self, text: str) -> List[Dict[str, str]]:
        """Find static CALL statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The called module name
            - text: The full text of the CALL statement
        """
        results = []

        # Find all static CALL statements
        for match in self.static_call_pattern.finditer(text):
            target = match.group(1).strip()
            call_text = match.group(0).strip()

            # Clean up the target name (remove quotes and spaces)
            target = target.strip('\'"')

            results.append({
                "target": target,
                "text": call_text
            })

        return results

    def _find_dynamic_calls(self, text: str) -> List[Dict[str, str]]:
        """Find dynamic CALL statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The variable name
            - text: The full text of the CALL statement
        """
        results = []

        # Find all dynamic CALL statements
        for match in self.dynamic_call_pattern.finditer(text):
            target = match.group(1).strip()
            call_text = match.group(0).strip()

            # Skip if the target looks like a string literal
            if target.startswith('"') or target.startswith("'"):
                continue

            results.append({
                "target": target,
                "text": call_text
            })

        return results
