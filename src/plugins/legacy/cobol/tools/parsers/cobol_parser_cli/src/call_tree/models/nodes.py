import uuid
from typing import Dict, Any, Optional


class NodeBase:
    """Base class for all call tree nodes"""
    
    def __init__(self):
        """Initialize the base node with a UUID"""
        self.uuid = str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary representation
        
        Returns:
            Dictionary representation of the node
        """
        raise NotImplementedError("Subclass must implement to_dict()")


class MissingCobolModule(NodeBase):
    """Represents a missing COBOL module that is referenced but not found"""
    
    def __init__(self, module_id: str):
        """Initialize a missing COBOL module node
        
        Args:
            module_id: The name/ID of the missing module
        """
        super().__init__()
        self.module_id = module_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary representation
        
        Returns:
            Dictionary representation of the node
        """
        return {
            "type": "MissingCobolModule",
            "uuid": self.uuid,
            "module_id": self.module_id
        }


class MissingCobolParagraph(NodeBase):
    """Represents a missing COBOL paragraph that is referenced but not found"""
    
    # COBOL keywords that should not be treated as paragraph names
    COBOL_KEYWORDS = [
        "ACCEPT", "ADD", "ALTER", "CALL", "CANCEL", "CLOSE", "COMPUTE", "CONTINUE", 
        "DELETE", "DISPLAY", "DIVIDE", "ELSE", "END", "END-IF", "END-PERFORM", 
        "EVALUATE", "EXIT", "GO", "GOBACK", "IF", "INITIALIZE", "INSPECT", "MOVE", 
        "MULTIPLY", "NEXT", "OPEN", "PERFORM", "READ", "REWRITE", "SEARCH", "SET", 
        "STOP", "STRING", "SUBTRACT", "UNSTRING", "UNTIL", "VARYING", "WRITE", "EXEC"
    ]
    
    @classmethod
    def is_keyword(cls, name: str) -> bool:
        """Check if a name is a COBOL keyword
        
        Args:
            name: Name to check
            
        Returns:
            True if name is a COBOL keyword, False otherwise
        """
        return name.upper() in [k.upper() for k in cls.COBOL_KEYWORDS]
    
    def __init__(self, module_id: str, name: str):
        """Initialize a missing COBOL paragraph node"""
        super().__init__()
        self.module_id = module_id
        self.name = name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary representation
        
        Returns:
            Dictionary representation of the node
        """
        return {
            "type": "MissingCobolParagraph",
            "uuid": self.uuid,
            "module_id": self.module_id,
            "name": self.name
        }


class CobolVariableTarget(NodeBase):
    """Represents a variable used as a target in a CALL statement"""
    ## TODO Maybe we will have to add module_id as a first param
    def __init__(self, variable_name: str, source_text: str, usage_context: str):
        """Initialize a COBOL variable target node
        
        Args:
            variable_name: The name of the variable containing the target
            source_text: The full text of the statement containing the variable target
            usage_context: The context where this variable target is used (e.g., "CALL")
        """
        super().__init__()
        self.name = variable_name ## TODO Maybe we can replace variable_name, paragraph_name, etc. with just `name` everywhere
        self.variable_name = variable_name
        self.source_text = source_text
        self.usage_context = usage_context
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary representation
        
        Returns:
            Dictionary representation of the node
        """
        return {
            "type": "CobolVariableTarget",
            "uuid": self.uuid,
            "name": self.name,
            "variable_name": self.variable_name,
            "source_text": self.source_text,
            "usage_context": self.usage_context
        }


class CicsCommand(NodeBase):
    """Represents an EXEC CICS command"""
    
    def __init__(self, command_text: str, command_type: str):
        """Initialize a CICS command node
        
        Args:
            command_text: The full text of the CICS command
            command_type: The type of CICS command (e.g., READ, WRITE, SEND)
        """
        super().__init__()
        self.command_text = command_text
        self.command_type = command_type
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the node to a dictionary representation
        
        Returns:
            Dictionary representation of the node
        """
        return {
            "type": "CicsCommand",
            "uuid": self.uuid,
            "command_text": self.command_text,
            "command_type": self.command_type
        }