import logging
import uuid
from typing import Dict, List, Any, Optional

from lark import Transformer, Tree

class AstBuilder(Transformer):
    """Converts Lark parse tree to our desired AST structure"""
    
    def __init__(self, module_id: str, file_name: str, full_text: str):
        """Initialize the AST builder
        
        Args:
            module_id: Unique identifier for the module
            file_name: Original source file name
            full_text: Complete text of the module
        """
        super().__init__()
        self.module_id = module_id
        self.file_name = file_name
        self.full_text = full_text
        self.logger = logging.getLogger(__name__)
        self.unnamed_seq = 0

    def transform(self, tree):
        """Transform a Lark parse tree or dictionary into our AST

        Args:
            tree: The parse tree or pre-built dictionary

        Returns:
            The transformed AST
        """
        # If tree is already a dictionary (from regex parser), use it directly
        if isinstance(tree, dict):
            return tree

        # For any other type (Lark Tree, etc), create a simplified module
        self.logger.info("Creating AST from pre-built tree")

        # Create a simplified module
        module = {
            "type": "CobolModule",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "file_name": self.file_name,
            "full_text": self.full_text,
            "children": []
        }

        # If it's a Lark Tree that's been processed by _create_fallback_ast method
        # Extract the divisions from it
        if isinstance(tree, Tree):
            try:
                # Get divisions content if available
                divisions_content = {}
                if hasattr(tree, 'divisions_content'):
                    divisions_content = tree.divisions_content

                # Get the procedure division content (fixes the unresolved reference)
                procedure_content = divisions_content.get('procedure', '')

                # If this is a Tree with a 'program' node that contains a 'cobol_module'
                if tree.data == 'program' and tree.children and isinstance(tree.children[0], Tree):
                    for div_node in tree.children[0].children:
                        if isinstance(div_node, Tree):
                            if div_node.data == 'identification_division':
                                ident_div = {
                                    "type": "CobolIdentificationDivision",
                                    "uuid": self._generate_uuid(),
                                    "name": "IDENTIFICATION DIVISION",
                                    "module_id": self.module_id,
                                    "full_text": divisions_content.get('identification', "[Full text from identification division]"),
                                    "program_id": self.module_id
                                }

                                # Look for program_id
                                for child in div_node.children:
                                    if isinstance(child, Tree) and child.data == 'program_id_entry':
                                        for token in child.children:
                                            if token.type == 'IDENTIFIER':
                                                ident_div["program_id"] = token.value

                                module["children"].append(ident_div)

                            elif div_node.data == 'environment_division':
                                env_div = {
                                    "type": "CobolEnvironmentDivision",
                                    "uuid": self._generate_uuid(),
                                    "name": "ENVIRONMENT DIVISION",
                                    "module_id": self.module_id,
                                    "full_text": divisions_content.get('environment', "[Full text from environment division]")
                                }
                                module["children"].append(env_div)

                            elif div_node.data == 'data_division':
                                data_div = {
                                    "type": "CobolDataDivision",
                                    "uuid": self._generate_uuid(),
                                    "name": "DATA DIVISION",
                                    "module_id": self.module_id,
                                    "full_text": divisions_content.get('data', "[Full text from data division]"),
                                    "children": []  # Initialize the children array for data division
                                }
                                # Adding data division sections
                                data_sections = divisions_content.get("data_sections", {})
                                if divisions_content and data_sections:
                                    for data_section_name in data_sections.keys():
                                        section_name = f"{data_section_name.upper()}-SECTION"
                                        section_content = data_sections.get(data_section_name)

                                        # Ensure section content has the section header included
                                        if data_section_name == 'file':
                                            header = 'FILE SECTION'
                                        elif data_section_name == 'working_storage':
                                            header = 'WORKING-STORAGE SECTION'
                                        elif data_section_name == 'local_storage':
                                            header = 'LOCAL-STORAGE SECTION'
                                        elif data_section_name == 'linkage':
                                            header = 'LINKAGE SECTION'
                                        else:
                                            header = f"{data_section_name.upper()} SECTION"

                                        # Ensure the section content starts with the header
                                        if header not in section_content:
                                            section_content = f"{header}.\n{section_content}"

                                        data_section = {
                                            "type": "CobolSection",
                                            "uuid": self._generate_uuid(),
                                            "name": section_name,
                                            "module_id": self.module_id,
                                            "section_name": section_name,
                                            "full_text": section_content,
                                            "parent_name": "DATA DIVISION",
                                            "is_unnamed": False
                                        }
                                        data_div["children"].append(data_section)
                                
                                # Even if no sections were explicitly found in the data division,
                                # we should create default sections since we know they exist in the code
                                if not data_sections and divisions_content.get('data'):
                                    # Parse the data division content to find standard sections
                                    data_content = divisions_content.get('data', '')
                                    
                                    # Look for common data sections
                                    sections_to_check = [
                                        ('FILE', 'FILE SECTION'),
                                        ('WORKING-STORAGE', 'WORKING-STORAGE SECTION'), 
                                        ('LOCAL-STORAGE', 'LOCAL-STORAGE SECTION'),
                                        ('LINKAGE', 'LINKAGE SECTION')
                                    ]
                                    
                                    # First find all section positions
                                    section_positions = []
                                    for section_name, section_header in sections_to_check:
                                        match = data_content.find(section_header)
                                        if match >= 0:
                                            section_positions.append((match, section_name, section_header))
                                    
                                    # Sort by position in the data content
                                    section_positions.sort(key=lambda x: x[0])
                                    
                                    # Extract each section's full content
                                    for i, (pos, section_name, section_header) in enumerate(section_positions):
                                        # Find the end of this section (start of next section or end of data_content)
                                        if i < len(section_positions) - 1:
                                            next_pos = section_positions[i+1][0]
                                            section_text = data_content[pos:next_pos]
                                        else:
                                            section_text = data_content[pos:]
                                        
                                        self.logger.info(f"Found {section_header} in data division at position {pos}")
                                        data_section = {
                                            "type": "CobolSection",
                                            "uuid": self._generate_uuid(),
                                            "name": f"{section_name}-SECTION",
                                            "module_id": self.module_id,
                                            "section_name": f"{section_name}-SECTION",
                                            "full_text": section_text.strip(),
                                            "parent_name": "DATA DIVISION",
                                            "is_unnamed": False
                                        }
                                        data_div["children"].append(data_section)

                                module["children"].append(data_div)

                            elif div_node.data == 'procedure_division':
                                proc_div = {
                                    "type": "CobolProcedureDivision",
                                    "uuid": self._generate_uuid(),
                                    "name": "PROCEDURE DIVISION",
                                    "module_id": self.module_id,
                                    "full_text": divisions_content.get('procedure', "[Full text from procedure division]"),
                                    "children": []
                                }

                                # Log procedure section information for debugging
                                if hasattr(tree, 'divisions_content') and 'procedure_sections' in tree.divisions_content:
                                    self.logger.info(f"Found {len(tree.divisions_content['procedure_sections'])} procedure sections")
                                    for sec_name, sec_info in tree.divisions_content['procedure_sections'].items():
                                        self.logger.info(f"Section {sec_name} has {len(sec_info.get('paragraphs', {}))} paragraphs")

                                # Process each section in the procedure division
                                entry_paragraph_added = False

                                # Process the unnamed section first if it exists
                                if hasattr(tree, 'divisions_content') and 'procedure_sections' in tree.divisions_content:
                                    if 'unnamed_section' in tree.divisions_content['procedure_sections']:
                                        unnamed_section_info = tree.divisions_content['procedure_sections']['unnamed_section']

                                        # Create the unnamed section
                                        unnamed_section = {
                                            "type": "CobolSection",
                                            "uuid": self._generate_uuid(),
                                            "name": "UNNAMED-SECTION",
                                                "module_id": self.module_id,
                                            "section_name": "UNNAMED-SECTION",
                                            "full_text": unnamed_section_info.get('full_text', "UNNAMED-SECTION"),
                                            "parent_name": "PROCEDURE DIVISION",
                                            "is_unnamed": True,
                                            "children": []
                                        }

                                        # Add the entry paragraph if it exists
                                        if 'ENTRY-PARAGRAPH' in unnamed_section_info.get('paragraphs', {}):
                                            entry_para = {
                                                "type": "CobolEntryParagraph",
                                                "uuid": self._generate_uuid(),
                                                "name": "ENTRY-PARAGRAPH",
                                                "module_id": self.module_id,
                                                "paragraph_name": "ENTRY-PARAGRAPH",
                                                "entry_name": self.module_id,
                                                "full_text": unnamed_section_info['paragraphs']['ENTRY-PARAGRAPH'],
                                                "parent_name": "UNNAMED-SECTION",
                                                "is_main_entry": True,
                                                "is_unnamed": True
                                            }
                                            unnamed_section["children"].append(entry_para)
                                            entry_paragraph_added = True

                                        # Add other paragraphs in the unnamed section
                                        for para_name, para_content in unnamed_section_info.get('paragraphs', {}).items():
                                            if para_name == 'ENTRY-PARAGRAPH':
                                                continue

                                            # Create the paragraph node
                                            is_unnamed = para_name.startswith("UNNAMED_") or para_name == "UNNAMED-PARA"

                                            paragraph = {
                                                "type": "CobolParagraph",
                                                "uuid": self._generate_uuid(),
                                                "name": para_name,
                                                    "module_id": self.module_id,
                                                "paragraph_name": para_name,
                                                "full_text": para_content,
                                                "parent_name": "UNNAMED-SECTION",
                                                "is_unnamed": is_unnamed
                                            }
                                            unnamed_section["children"].append(paragraph)

                                        # Add the unnamed section to procedure division if it has any paragraphs
                                        if unnamed_section["children"]:
                                            proc_div["children"].append(unnamed_section)

                                # Process all other sections
                                if hasattr(tree, 'divisions_content') and 'procedure_sections' in tree.divisions_content:
                                    for sec_name, sec_info in tree.divisions_content['procedure_sections'].items():
                                        # Skip the unnamed section since we already processed it
                                        if sec_name == 'unnamed_section':
                                            continue

                                        # Create section
                                        section = {
                                            "type": "CobolSection",
                                            "uuid": self._generate_uuid(),
                                            "name": section_name,
                                            "module_id": self.module_id,
                                            "section_name": sec_info['name'],
                                            "full_text": f"{sec_info['name']} SECTION.",
                                            "parent_name": "PROCEDURE DIVISION",
                                            "is_unnamed": False,
                                            "children": []
                                        }

                                        # Add paragraphs to the section
                                        if sec_info.get('paragraphs'):
                                            for para_name, para_content in sec_info['paragraphs'].items():
                                                # Create paragraph
                                                is_unnamed = para_name.startswith("UNNAMED_") or para_name == "UNNAMED-PARA"

                                                paragraph = {
                                                    "type": "CobolParagraph",
                                                    "uuid": self._generate_uuid(),
                                                    "name": "ENTRY-PARAGRAPH",
                                                        "module_id": self.module_id,
                                                    "paragraph_name": para_name,
                                                    "full_text": para_content,
                                                    "parent_name": sec_info['name'],
                                                    "is_unnamed": is_unnamed
                                                }
                                                section["children"].append(paragraph)
                                        else:
                                            # If no paragraphs, create an unnamed paragraph with the section's content
                                            section_text = f"{sec_info['name']} SECTION."
                                            # Try to get the full section text from the original source if available
                                            section_start = procedure_content.find(f"{sec_info['name']} SECTION")
                                            if section_start != -1:
                                                next_section_start = float('inf')
                                                for other_sec_name in tree.divisions_content['procedure_sections']:
                                                    if other_sec_name != sec_name and other_sec_name != 'unnamed_section':
                                                        other_section_start = procedure_content.find(
                                                            f"{tree.divisions_content['procedure_sections'][other_sec_name]['name']} SECTION",
                                                            section_start + 1
                                                        )
                                                        if other_section_start != -1 and other_section_start < next_section_start:
                                                            next_section_start = other_section_start

                                                if next_section_start < float('inf'):
                                                    section_text = procedure_content[section_start:next_section_start].strip()
                                                else:
                                                    section_text = procedure_content[section_start:].strip()

                                            unnamed_para = {
                                                "type": "CobolParagraph",
                                                "uuid": self._generate_uuid(),
                                                "module_id": self.module_id,
                                                "paragraph_name": f"UNNAMED-PARA-{sec_name}",
                                                "full_text": section_text,
                                                "parent_name": sec_info['name'],
                                                "is_unnamed": True
                                            }
                                            section["children"].append(unnamed_para)

                                        # Add section to procedure division
                                        proc_div["children"].append(section)

                                # If no sections were found, create an unnamed section with an entry paragraph
                                if not proc_div["children"]:
                                    self.logger.warning("No sections found in procedure division, creating default unnamed section")
                                    unnamed_section = {
                                        "type": "CobolSection",
                                        "uuid": self._generate_uuid(),
                                                        "name": paragraph_name,
                                        "module_id": self.module_id,
                                        "section_name": "UNNAMED-SECTION",
                                        "full_text": "UNNAMED-SECTION",
                                        "parent_name": "PROCEDURE DIVISION",
                                        "is_unnamed": True,
                                        "children": []
                                    }

                                    # Add an entry paragraph with the full procedure division content
                                    if procedure_content:
                                        entry_para = {
                                            "type": "CobolEntryParagraph",
                                            "uuid": self._generate_uuid(),
                                                        "name": paragraph_name,
                                            "module_id": self.module_id,
                                            "paragraph_name": "ENTRY-PARAGRAPH",
                                            "entry_name": self.module_id,
                                            "full_text": procedure_content,
                                            "parent_name": "UNNAMED-SECTION",
                                            "is_main_entry": True,
                                            "is_unnamed": True
                                        }
                                        unnamed_section["children"].append(entry_para)

                                        # Add the unnamed section
                                        proc_div["children"].append(unnamed_section)
                                module["children"].append(proc_div)
            except Exception as e:
                self.logger.warning(f"Error extracting divisions from Tree: {str(e)}")

        return module
    
    def _generate_uuid(self) -> str:
        """Generate a unique UUID v4 string"""
        return str(uuid.uuid4())
    
    def _get_full_text_for_node(self, node: Tree) -> str:
        """Extract the full text for a node from the original source
        
        This would use the meta information in a real implementation to get the
        text range from the original source. For this implementation, we'll just
        return a placeholder.
        """
        # In a real implementation, we would use node.meta information to extract
        # the correct text range from self.full_text
        return f"[Full text for {node.data} would be extracted here]"
    
    def _next_unnamed_seq(self) -> int:
        """Get the next sequence number for unnamed elements"""
        self.unnamed_seq += 1
        return self.unnamed_seq
    
    def cobol_module(self, items):
        """Transform the cobol_module node"""
        # Find each division
        identification_div = next((i for i in items if i["type"] == "CobolIdentificationDivision"), None)
        environment_div = next((i for i in items if i["type"] == "CobolEnvironmentDivision"), None)
        data_div = next((i for i in items if i["type"] == "CobolDataDivision"), None)
        procedure_div = next((i for i in items if i["type"] == "CobolProcedureDivision"), None)
        
        # Create module node
        module = {
            "type": "CobolModule",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "file_name": self.file_name,
            "full_text": self.full_text,
            "children": []
        }
        
        # Add divisions as children
        if identification_div:
            module["children"].append(identification_div)
        
        if environment_div:
            module["children"].append(environment_div)
        
        if data_div:
            module["children"].append(data_div)
        
        if procedure_div:
            module["children"].append(procedure_div)
        
        return module
    
    def identification_division(self, items):
        """Transform the identification_division node"""
        # Extract program ID
        program_id = ""
        for item in items:
            if isinstance(item, dict) and item.get("type") == "program_id_entry":
                program_id = item.get("program_name", "")
                break
        
        return {
            "type": "CobolIdentificationDivision",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "full_text": self._get_full_text_for_node(Tree('identification_division', items)),
            "program_id": program_id
        }
    
    def program_id_entry(self, items):
        return {
            "type": "program_id_entry",
            "program_name": items[0].strip('"\'')
        }
    
    def environment_division(self, items):
        """Transform the environment_division node"""
        return {
            "type": "CobolEnvironmentDivision",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "full_text": self._get_full_text_for_node(Tree('environment_division', items))
        }
    
    def data_division(self, items):
        """Transform the data_division node"""
        return {
            "type": "CobolDataDivision",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "full_text": self._get_full_text_for_node(Tree('data_division', items))
        }
    
    def procedure_division(self, items):
        """Transform the procedure_division node"""
        # Process sections and paragraphs
        sections = []
        is_first_section = True
        
        # Check if there are explicit sections or just paragraphs at the top level
        has_sections = any(isinstance(i, dict) and i.get("type") == "CobolSection" for i in items)
        
        # If there are no explicit sections, create an unnamed section
        if not has_sections:
            paragraphs = [i for i in items if isinstance(i, dict) and i.get("type") == "CobolParagraph"]
            if paragraphs:
                section_name = f"UNNAMED_{self.module_id}_{self._next_unnamed_seq()}"
                section_text = "\n".join(p.get("full_text", "") for p in paragraphs)
                
                # Create the unnamed section
                unnamed_section = {
                    "type": "CobolSection",
                    "uuid": self._generate_uuid(),
                    "module_id": self.module_id,
                    "section_name": section_name,
                    "full_text": section_text,
                    "parent_name": "PROCEDURE DIVISION",
                    "is_unnamed": True,
                    "children": []
                }
                
                # Process paragraphs within this unnamed section
                for i, paragraph in enumerate(paragraphs):
                    # Update paragraph's parent name
                    paragraph["parent_name"] = section_name
                    
                    # First paragraph becomes an entry paragraph
                    if i == 0 and is_first_section:
                        entry_name = ""
                        for div in items:
                            if isinstance(div, dict) and div.get("type") == "CobolIdentificationDivision":
                                entry_name = div.get("program_id", "")
                                break
                        
                        # Convert to entry paragraph
                        entry_paragraph = {
                            "type": "CobolEntryParagraph",
                            "uuid": self._generate_uuid(),
                            "module_id": self.module_id,
                            "paragraph_name": paragraph.get("paragraph_name", f"UNNAMED_ENTRY_{self.module_id}"),
                            "entry_name": entry_name,
                            "full_text": paragraph.get("full_text", ""),
                            "parent_name": section_name,
                            "is_main_entry": True,
                            "is_unnamed": paragraph.get("is_unnamed", False)
                        }
                        unnamed_section["children"].append(entry_paragraph)
                    else:
                        unnamed_section["children"].append(paragraph)
                
                sections.append(unnamed_section)
        else:
            # Process explicit sections
            for section in [i for i in items if isinstance(i, dict) and i.get("type") == "CobolSection"]:
                # Process paragraphs in this section
                if is_first_section and section["children"]:
                    # First paragraph of first section becomes an entry paragraph
                    first_paragraph = section["children"][0]
                    entry_name = ""
                    for div in items:
                        if isinstance(div, dict) and div.get("type") == "CobolIdentificationDivision":
                            entry_name = div.get("program_id", "")
                            break
                    
                    # Convert to entry paragraph
                    entry_paragraph = {
                        "type": "CobolEntryParagraph",
                        "uuid": self._generate_uuid(),
                        "module_id": self.module_id,
                        "paragraph_name": first_paragraph.get("paragraph_name", f"UNNAMED_ENTRY_{self.module_id}"),
                        "entry_name": entry_name,
                        "full_text": first_paragraph.get("full_text", ""),
                        "parent_name": section.get("section_name", ""),
                        "is_main_entry": True,
                        "is_unnamed": first_paragraph.get("is_unnamed", False)
                    }
                    
                    # Replace first paragraph with entry paragraph
                    section["children"][0] = entry_paragraph
                
                sections.append(section)
                is_first_section = False
        
        return {
            "type": "CobolProcedureDivision",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "full_text": self._get_full_text_for_node(Tree('procedure_division', items)),
            "children": sections
        }
    
    def section(self, items):
        """Transform a section node"""
        section_header = items[0] if items else {}
        section_name = section_header.get("section_name", f"UNNAMED_{self.module_id}_{self._next_unnamed_seq()}")
        is_unnamed = "section_name" not in section_header
        
        # Process paragraphs
        paragraphs = [i for i in items[1:] if isinstance(i, dict) and i.get("type") == "CobolParagraph"]
        
        return {
            "type": "CobolSection",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "section_name": section_name,
            "full_text": self._get_full_text_for_node(Tree('section', items)),
            "parent_name": "PROCEDURE DIVISION",
            "is_unnamed": is_unnamed,
            "children": paragraphs
        }
    
    def section_header(self, items):
        """Transform a section header node"""
        return {"section_name": items[0]} if items else {}
    
    def paragraph(self, items):
        """Transform a paragraph node"""
        paragraph_header = items[0] if items else {}
        paragraph_name = paragraph_header.get("paragraph_name", f"UNNAMED_{self.module_id}_{self._next_unnamed_seq()}")
        is_unnamed = "paragraph_name" not in paragraph_header
        
        return {
            "type": "CobolParagraph",
            "uuid": self._generate_uuid(),
            "module_id": self.module_id,
            "paragraph_name": paragraph_name,
            "full_text": self._get_full_text_for_node(Tree('paragraph', items)),
            "parent_name": "",  # Will be set by the parent section
            "is_unnamed": is_unnamed
        }
    
    def paragraph_header(self, items):
        """Transform a paragraph header node"""
        return {"paragraph_name": items[0]} if items else {}
