import os
import re
import logging
from typing import Dict, List, Set, Optional, Tuple

class CopybookExpander:
    """Handles COBOL copybook expansion"""

    def __init__(self, copybook_dirs: List[str], max_recursion_depth: int = 10):
        """Initialize the copybook expander

        Args:
            copybook_dirs: List of directories containing copybooks
            max_recursion_depth: Maximum recursion depth for copybook expansion
        """
        self.copybook_dirs = copybook_dirs
        self.max_recursion_depth = max_recursion_depth
        self.cache: Dict[str, str] = {}  # Cache of expanded copybooks
        self.logger = logging.getLogger(__name__)

    def expand_source(self, source_text: str, file_name: str) -> str:
        """Expand copybooks in the given source text ## TODO Strange code call, to just call the same, figure out why?

        Args:
            source_text: The COBOL source text
            file_name: The name of the source file (for error reporting)

        Returns:
            The expanded source text
        """
        self.logger.info(f"Expanding copybooks in {file_name}")
        expanded_text, _ = self._expand_copybooks(source_text, file_name, set(), 0)
        return expanded_text

    def _expand_copybooks(self, source_text: str, file_name: str,
                          recursively_included_files: Set[str], depth: int) -> Tuple[str, Set[str]]:
        """Recursively expand copybooks in the source text ## TODO Better replace recursion with iterations over stack or queue

        Args:
            source_text: The COBOL source text
            file_name: The name of the source file
            recursively_included_files: Set of already included files
            depth: Current recursion depth

        Returns:
            Tuple of (expanded text, set of included files)
        """
        if depth > self.max_recursion_depth:
            self.logger.warning(f"Maximum recursion depth reached in {file_name}. "
                                f"Some copybooks might not be expanded.")
            return source_text, recursively_included_files

        # Regular expression to match COPY statements
        # Updated to handle COBOL line numbers (columns 1-6) and continuation indicator (column 7)
        # Format: [line-numbers][continuation] COPY copybook_name [REPLACING [[==text1==] BY [==text2==]] ...]
        # Also supports quoted copybook names: COPY 'copybook_name' or COPY "copybook_name"
        copy_pattern = re.compile(
            r'^.{0,7}\s*COPY\s+(?:\'([A-Za-z0-9-]+)\'|\"([A-Za-z0-9-]+)\"|([A-Za-z0-9-]+))(?:\s+REPLACING\s+(.+?))?\s*\.?\s*\d*$',
            re.IGNORECASE | re.DOTALL
        )

        # Pattern to match the start of a COPY statement that might span multiple lines
        # Updated to handle line numbers
        copy_start_pattern = re.compile(r'^.{0,7}\s*COPY\s*$', re.IGNORECASE)

        # Pattern for COBOL continuation line (has hyphen in column 7)
        continuation_pattern = re.compile(r'^.{6}-', re.MULTILINE)

        lines = source_text.splitlines()
        result_lines = []

        line_number = 0
        while line_number < len(lines):
            line = lines[line_number]

            # Check if this is a complete COPY statement on a single line
            match = copy_pattern.match(line)

            # Debug log all lines containing COPY
            if "COPY" in line:
                self.logger.debug(f"Found line with COPY: '{line}'")
                self.logger.debug(f"Match result: {bool(match)}")
                if match:
                    self.logger.debug(f"Match groups: {[match.group(i) for i in range(5) if match.group(i) is not None]}")

            # Check if this is the start of a multi-line COPY statement
            if not match and copy_start_pattern.match(line):
                # This is the start of a multi-line COPY statement
                self.logger.debug(f"Found start of multi-line COPY statement: '{line}'")
                # Collect the following lines until we have a complete COPY statement
                complete_copy_statement = line
                temp_line_number = line_number + 1

                while temp_line_number < len(lines):
                    next_line = lines[temp_line_number]

                    # Check if it's a continuation line (has a hyphen in column 7)
                    if continuation_pattern.match(next_line):
                        # It's a continuation line, combine it
                        self.logger.debug(f"Found continuation line: '{next_line}'")
                        # Remove the hyphen and whitespace
                        if len(next_line) > 7:  # Make sure the line is long enough
                            content_part = next_line[7:]  # Skip the hyphen
                            complete_copy_statement += " " + content_part.rstrip()
                            self.logger.debug(f"Combined statement so far: '{complete_copy_statement}'")
                        temp_line_number += 1

                        # Try matching the combined statement
                        match = copy_pattern.match(complete_copy_statement)
                        if match:
                            # Found a complete statement, update line_number to skip these lines
                            self.logger.debug(f"Successfully matched multi-line COPY statement: '{complete_copy_statement}'")
                            line_number = temp_line_number - 1  # -1 because we increment at the end
                            break
                    else:
                        # Not a continuation, stop combining
                        self.logger.debug(f"Not a continuation line, stopping: '{next_line}'")
                        break

            if not match:
                result_lines.append(line)
                line_number += 1
                continue

            # Extract copybook name and replacements
            # We have 3 capturing groups for copybook name (single quote, double quote, or no quotes)
            # Use the first non-None group
            copybook_name = next((group for group in [match.group(1), match.group(2), match.group(3)] if group), None)
            replacements_text = match.group(4)

            # Debug logging for matched copybooks
            self.logger.debug(f"Found COPY statement for copybook: '{copybook_name}'")

            # Find and read the copybook
            copybook_content = self._find_copybook(copybook_name)
            if copybook_content is None:
                self.logger.error(f"Copybook {copybook_name} not found, will be skipped.")
                result_lines.append(f"      * ERROR: COPYBOOK {copybook_name} NOT FOUND, SKIPPED.")
                line_number += 1
                continue
            elif not copybook_content.strip():
                self.logger.warning(f"Copybook {copybook_name} is empty (has no content)")
                result_lines.append(f"      * WARNING: COPYBOOK {copybook_name} IS EMPTY.")
                line_number += 1
                continue

            # Apply replacements if specified
            if replacements_text:
                copybook_content = self._apply_replacements(copybook_content, replacements_text)

            # Track recursively included files (parents) to avoid circular dependencies
            if copybook_name in recursively_included_files:
                self.logger.warning(f"Circular dependency detected: {copybook_name} "
                                    f"already included in {file_name}, the circular COPYBOOK insert will be skipped.")
                result_lines.append(f"      * WARNING: CIRCULAR DEPENDENCY IN COPYBOOK {copybook_name}, THE COPYBOOK INSERT WILL BE IGNORED.")
                line_number += 1
                continue

            # Recursively expand copybooks in the included content
            current_recursively_included = recursively_included_files.copy() # TODO ?? Is this needed ??
            current_recursively_included.add(copybook_name)
            expanded_content, new_included = self._expand_copybooks(
                copybook_content, copybook_name, current_recursively_included, depth + 1
            )
            current_recursively_included.discard(copybook_name) # To avoid false recursion detection, remove the copybook we just processed
            # recursively_included_files.update(new_included)

            # Add source mapping comment before and after the expanded content
            result_lines.append(f"      * START OF COPYBOOK: {copybook_name}")
            result_lines.extend(expanded_content.splitlines())
            result_lines.append(f"      * END OF COPYBOOK: {copybook_name}")

            line_number += 1

        return "\n".join(result_lines), recursively_included_files

    def _find_copybook(self, copybook_name: str) -> Optional[str]:
        """Find and read a copybook

        Args:
            copybook_name: The name of the copybook

        Returns:
            The content of the copybook or None if not found
        """
        self.logger.debug(f"Looking for copybook: {copybook_name}")

        # Check cache first
        if copybook_name in self.cache:
            self.logger.debug(f"Using cached copybook: {copybook_name}")
            return self.cache[copybook_name]

        # Look in all copybook directories
        for directory in self.copybook_dirs:
            if not directory:
                self.logger.warning(f"Empty copybook directory specified")
                continue

            self.logger.debug(f"Searching in directory: {directory}")

            # Try different extensions (.cpy, .cbl, .cob, no extension)
            for ext in ["", ".cpy", ".CPY", ".cob", ".COBOL", ".cobol", ".COB", ".cbl", ".CBL"]:
                file_path = os.path.join(directory, f"{copybook_name}{ext}")
                self.logger.debug(f"Trying path: {file_path}")

                if os.path.isfile(file_path):
                    try:
                        with open(file_path, 'r') as file:
                            content = file.read()
                            self.cache[copybook_name] = content
                            self.logger.debug(f"Found copybook: {file_path}")
                            self.logger.debug(f"Content (first 100 chars): {content[:100]}...")
                            return content
                    except Exception as e:
                        self.logger.error(f"Error reading copybook {file_path}: {str(e)}")
                else:
                    self.logger.debug(f"File not found: {file_path}")

        self.logger.warning(f"Copybook not found: {copybook_name}, searched in {self.copybook_dirs}")
        return None

    def _apply_replacements(self, text: str, replacements_text: str) -> str:
        """Apply REPLACING clause replacements to copybook text

        Args:
            text: The copybook text
            replacements_text: The REPLACING clause text

        Returns:
            The text with replacements applied
        """
        # Parse the replacements text
        # Format: ==text1== BY ==text2== ==text3== BY ==text4== ...
        replacements = []
        parts = re.split(r'\s+BY\s+', replacements_text)

        if len(parts) % 2 != 0:
            self.logger.warning(f"Invalid REPLACING clause: {replacements_text}")
            return text

        for i in range(0, len(parts), 2):
            source = parts[i].strip()
            target = parts[i + 1].strip()

            # Handle delimited identifiers (==text==)
            if source.startswith('==') and source.endswith('=='):
                source = source[2:-2]

            if target.startswith('==') and target.endswith('=='):
                target = target[2:-2]

            replacements.append((source, target))

        # Apply replacements
        result = text
        for source, target in replacements:
            result = result.replace(source, target)

        return result