import re
from typing import Dict, Any, <PERSON>, Tuple

from src.platform.tools.data.kuzu import KuzuConnector
from ..models.nodes import CicsCommand
from ..models.relationships import CicsRelationship
from .base import BaseAnalyzer


## TODO Refactoring: we should extract all database operation from here to a Data layer abstract class
##  and implement them behind that class, implementing concrete DB connector classes
class CicsAnalyzer(BaseAnalyzer):
    """Analyzer for EXEC CICS statements in COBOL code"""

    def __init__(self, graph_db_connector: KuzuConnector):
        """Initialize the CICS statement analyzer

        Args:
            graph_db_connector: graph database connector instance
        """
        super().__init__(graph_db_connector)

        # Regular expression for CICS statements
        self.cics_pattern = re.compile(
            r'(?i)EXEC\s+CICS\s+(.*?)(?:END-EXEC|\.$)',
            re.DOTALL
        )

        # Pattern to extract CICS command type
        self.cics_command_type_pattern = re.compile(
            r'(?i)^\s*([A-Za-z0-9_-]+)'
        )

    def analyze(self) -> Tuple[int, int]:
        """Analyze EXEC CICS statements in COBOL code

        Returns:
            Tuple of (nodes_created, relationships_created)
        """
        self.logger.info("Starting EXEC CICS statement analysis")

        # Get all modules
        modules = self._get_all_modules()
        self.logger.info(f"Found {len(modules)} modules to analyze")

        nodes_created = 0
        relationships_created = 0

        # Process each module
        for module in modules:
            module_id = module["module_id"]

            self.logger.info(f"Processing module: {module_id}")

            # Get all paragraphs in the module
            paragraphs = self._find_paragraphs_in_module(module_id)
            self.logger.info(f"Found {len(paragraphs)} paragraphs in module {module_id}")

            # Process each paragraph
            for paragraph in paragraphs:

                if not paragraph['full_text']:
                    self.logger.warning(f"Paragraph {paragraph['name']} in {module_id} has no text")
                    continue

                # Process CICS statements
                cics_commands = self._find_cics_commands(paragraph['full_text'])
                for command in cics_commands:
                    command_text = command["text"]
                    command_type = command["type"]

                    # Create CicsCommand node
                    cics_command = CicsCommand(
                        command_text=command_text,
                        command_type=command_type
                    )
                    cics_command_dict = cics_command.to_dict()
                    cics_command_uuid = self._create_node(cics_command_dict)

                    if cics_command_uuid:
                        nodes_created += 1

                        # Create a CICS relationship
                        cics_rel = CicsRelationship(
                            from_uuid=paragraph["uuid"],
                            from_type=paragraph["type_label"],
                            to_uuid=cics_command_uuid,
                            to_type="CobolParagraph"
                        )
                        cics_rel_dict = cics_rel.to_dict()
                        if self._create_relationship(cics_rel_dict):
                            relationships_created += 1

        self.logger.info(f"CICS analysis completed: Created {nodes_created} nodes and {relationships_created} relationships")
        return nodes_created, relationships_created

    def _get_all_modules(self) -> List[Dict[str, Any]]:
        """Get all COBOL modules in the database

        Returns:
            List of modules
        """
        query = """
        MATCH (m:CobolModule)
        RETURN m.uuid AS uuid, m.module_id AS module_id
        """

        return self._execute_cypher(query)

    def _find_cics_commands(self, text: str) -> List[Dict[str, str]]:
        """Find CICS commands in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - text: The full text of the CICS command
            - type: The type of CICS command (e.g., READ, WRITE, SEND)
        """
        results = []

        # Find all CICS statements
        for match in self.cics_pattern.finditer(text):
            command_content = match.group(1).strip()
            command_text = match.group(0).strip()

            # Extract the command type from the content
            type_match = self.cics_command_type_pattern.search(command_content)
            command_type = type_match.group(1).upper() if type_match else "UNKNOWN"

            results.append({
                "text": command_text,
                "type": command_type
            })

        return results