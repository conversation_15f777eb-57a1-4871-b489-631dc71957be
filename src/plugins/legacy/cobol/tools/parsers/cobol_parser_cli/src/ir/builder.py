import logging
from typing import Dict, List, Any, Optional, Tuple

from ..parser.visitors import (
    BaseVisitor, ModuleVisitor, DivisionVisitor,
    SectionVisitor, ParagraphVisitor
)

class IRBuilder:
    """Builds an intermediate representation from an AST"""
    
    def __init__(self):
        """Initialize the IR builder"""
        self.logger = logging.getLogger(__name__)
        
        # Create visitors
        self.module_visitor = ModuleVisitor()
        self.division_visitor = DivisionVisitor()
        self.section_visitor = SectionVisitor()
        self.paragraph_visitor = ParagraphVisitor()
    
    def build(self, ast: Any) -> Dict[str, Any]:
        """Build an intermediate representation from an AST
        
        Args:
            ast: The abstract syntax tree (can be Dict or Lark Tree)
            
        Returns:
            The intermediate representation
        """
        if not ast:
            self.logger.error("Cannot build IR from empty AST")
            return {}
        
        # Convert Lark Tree to dictionary if needed
        if not isinstance(ast, dict):
            # Create a simple representation for non-dict ASTs
            self.logger.info("Converting non-dict AST to IR format")
            processed_ast = {
                "type": "CobolModule",
                "module_id": getattr(ast, "module_id", "UNKNOWN"),
                "file_name": getattr(ast, "file_name", "UNKNOWN"),
                "children": []
            }
            
            # Extract divisions if available
            if hasattr(ast, "children") and ast.children:
                # Get the children from the tree
                for child in ast.children:
                    if hasattr(child, "data"):
                        if child.data == "identification_division":
                            processed_ast["children"].append({
                                "type": "CobolIdentificationDivision",
                                "module_id": processed_ast["module_id"],
                                "full_text": "",
                                "program_id": ""
                            })
                        elif child.data == "environment_division":
                            processed_ast["children"].append({
                                "type": "CobolEnvironmentDivision",
                                "module_id": processed_ast["module_id"],
                                "full_text": ""
                            })
                        elif child.data == "data_division":
                            processed_ast["children"].append({
                                "type": "CobolDataDivision",
                                "module_id": processed_ast["module_id"],
                                "full_text": ""
                            })
                        elif child.data == "procedure_division":
                            processed_ast["children"].append({
                                "type": "CobolProcedureDivision",
                                "module_id": processed_ast["module_id"],
                                "full_text": "",
                                "children": []
                            })
        else:
            # Apply visitors to process the AST
            processed_ast = self._apply_visitors(ast)
        
        # Extract relationship information
        nodes, relationships = self._extract_relationships(processed_ast)
        
        # Build the IR
        return {
            "metadata": {
                "module_id": processed_ast.get("module_id", ""),
                "file_name": processed_ast.get("file_name", ""),
            },
            "nodes": nodes,
            "relationships": relationships
        }
    
    def _apply_visitors(self, ast: Dict[str, Any]) -> Dict[str, Any]:
        """Apply visitors to process the AST
        
        Args:
            ast: The abstract syntax tree
            
        Returns:
            The processed AST
        """
        # Apply visitors in order ## TODO ?? Looks like they all do the same nothing changes along the way
        ast = self.module_visitor.visit(ast)
        ast = self.division_visitor.visit(ast)
        ast = self.section_visitor.visit(ast)
        ast = self.paragraph_visitor.visit(ast)
        
        return ast
    
    def _extract_relationships(self, ast: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Extract nodes and relationships from the processed AST
        
        Args:
            ast: The processed abstract syntax tree
            
        Returns:
            A tuple of (nodes, relationships)
        """
        # Collect all nodes and build relationships
        nodes = []
        relationships = []
        
        # Process the AST to extract nodes and relationships
        self._collect_nodes_and_relationships(ast, nodes, relationships)
        
        return nodes, relationships
    
    def _collect_nodes_and_relationships( # TODO ?? Recursion is not good - potential stack-overflow
        self, node: Any, nodes: List[Dict[str, Any]], 
        relationships: List[Dict[str, Any]], parent: Optional[Dict[str, Any]] = None
    ) -> None:
        """Recursively collect nodes and relationships from the AST
        
        Args:
            node: The current AST node (Dict or Lark Tree)
            nodes: The list of nodes to update
            relationships: The list of relationships to update
            parent: The parent node (if any)
        """
        if isinstance(node, dict):
            # Extract node data without children
            node_data = {k: v for k, v in node.items() if k != "children"}
            
            # Add node to the list
            nodes.append(node_data)
            
            # Create relationship if there's a parent
            if parent:
                relationship = {
                    "uuid": self._generate_uuid(),
                    "type": "INCLUDES",
                    "from_uuid": parent.get("uuid"),
                    "to_uuid": node.get("uuid")
                }
                relationships.append(relationship)
            
            # Process children recursively
            for child in node.get("children", []):
                self._collect_nodes_and_relationships(child, nodes, relationships, node) # TODO ?? Recursion is not good - potential stack-overflow
        else:
            # For non-dict nodes (like Lark Trees), we need to extract data differently
            self.logger.warning(f"Encountered non-dict node, skipping: {type(node)}")
    
    def _generate_uuid(self) -> str:
        """Generate a UUID for relationships"""
        import uuid
        return str(uuid.uuid4())
