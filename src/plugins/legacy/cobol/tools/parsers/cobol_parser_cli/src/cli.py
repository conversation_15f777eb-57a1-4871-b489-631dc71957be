import os
import sys
import click
import logging
from typing import List, Optional

from .config import Config
from .orchestrator import Orchestrator

@click.command()
@click.argument('input_paths', nargs=-1, type=click.Path(exists=True, file_okay=True, dir_okay=True))
@click.option('--config-file', '-c', type=click.Path(exists=True), help='Path to configuration file')
@click.option('--copybook-dir', type=click.Path(exists=True, file_okay=False, dir_okay=True), 
              help='Directory containing copybooks')
@click.option('--output-dir', type=click.Path(file_okay=False, dir_okay=True), 
              help='Directory for intermediate representation')
@click.option('--neo4j-url', help='Neo4j database URL')
@click.option('--neo4j-user', help='Neo4j username')
@click.option('--neo4j-password', help='Neo4j password')
@click.option('--log-level', type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                                    case_sensitive=False), help='Set logging level')
@click.option('--batch-size', type=int, help='Number of operations in one Neo4j transaction')
@click.option('--recursive', '-r', is_flag=True, help='Process directories recursively')
@click.option('--extensions', default='.cbl,.cobol,.COBOL,.cob,.CBL,.COB', help='Comma-separated list of file extensions to process (default: .cbl,.cob,.cobol,.COBOL,.CBL,.COB)')
def main(input_paths: List[str], config_file: Optional[str], recursive: bool, extensions: str, **options):
    """COBOL Parser for IBM ENTERPRISE COBOL source code.
    
    This program parses COBOL source files, builds an internal representation,
    and stores the parsed data in a Neo4j database.
    
    INPUT_PATHS: One or more COBOL source files or directories to process.
    If a directory is provided, all files with recognized COBOL extensions will be processed.
    """
    if not input_paths:
        click.echo("Error: No input paths provided. Use --help for usage information.")
        sys.exit(1)
    
    # Parse the extension list
    valid_extensions = [ext.strip() for ext in extensions.split(',') if ext.strip()]
    
    # Get the list of files to process
    files_to_process = []
    for path in input_paths:
        if os.path.isfile(path):
            files_to_process.append(path)
        elif os.path.isdir(path):
            # Process directory
            for root, dirs, files in os.walk(path):
                for file in files:
                    # Check if the file has a valid COBOL extension
                    file_ext = os.path.splitext(file)[1]
                    if file_ext in valid_extensions:
                        files_to_process.append(os.path.join(root, file))
                
                # If not recursive, don't process subdirectories
                if not recursive:
                    break
    
    if not files_to_process:
        click.echo("No COBOL files found in the specified paths.")
        sys.exit(1)
    
    # Load configuration
    config = Config(config_file)
    
    # Override configuration with command-line options
    if options.get('copybook_dir'):
        config.config_data.setdefault('directories', {})['copybooks'] = options['copybook_dir']
    
    if options.get('output_dir'):
        config.config_data.setdefault('directories', {})['output'] = options['output_dir']
    
    if options.get('neo4j_url'):
        config.config_data.setdefault('neo4j', {})['url'] = options['neo4j_url']
    
    if options.get('neo4j_user'):
        config.config_data.setdefault('neo4j', {})['user'] = options['neo4j_user']
    
    if options.get('neo4j_password'):
        config.config_data.setdefault('neo4j', {})['password'] = options['neo4j_password']
    
    if options.get('log_level'):
        config.config_data.setdefault('logging', {})['level'] = options['log_level']
    
    if options.get('batch_size'):
        config.config_data.setdefault('neo4j', {})['batch_size'] = options['batch_size']
    
    # Setup logging
    config.setup_logging()
    logger = logging.getLogger(__name__)
    
    # Create output directory if it doesn't exist
    output_dir = config.get('directories.output')
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        logger.info(f"Created output directory: {output_dir}")
    
    # Create orchestrator and process files
    try:
        orchestrator = Orchestrator(config)
        success, failure_count = orchestrator.process_files(files_to_process)
        
        if failure_count == 0:
            click.echo(f"Successfully processed {len(files_to_process)} files.")
        else:
            click.echo(f"Processed {len(files_to_process)} files: {len(files_to_process) - failure_count} succeeded, {failure_count} failed.")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error processing files: {str(e)}", exc_info=True)
        click.echo(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()