import logging
from typing import Dict, Any, List, Optional, Tuple

from src.platform.tools.data.kuzu import KuzuConnector


# noinspection PyMethodMayBeStatic
## TODO Refactoring: we should extract all database operation from here to a Data layer abstract class
##  and implement them behind that class, implementing concrete DB connector classes
class BaseAnalyzer:
    """Base class for all call tree analyzers"""

    def __init__(self, graph_db_connector: KuzuConnector):
        """Initialize the base analyzer

        Args:
            graph_db_connector: graph database connector instance
            # batch_size: Number of operations to perform in a single transaction
        """
        self.graph_db_connector = graph_db_connector
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

        # Track processed items to avoid duplicates
        self._processed_items = set()

    def analyze(self) -> Tuple[int, int]:
        """Perform analysis

        Returns:
            Tuple of (nodes_created, relationships_created)
        """
        raise NotImplementedError("Subclass must implement analyze()")

    def _execute_cypher(self, query: str, **params) -> List[Dict[str, Any]] | None:
        """Execute a Cypher query

        Args:
            query: The Cypher query to execute
            params: Parameters for the query

        Returns:
            List of query results
        """
        try:
            # Execute the query and convert the result to a list of dictionaries
            response = self.graph_db_connector.connection.execute(query, **params)
            nodes = []
            while response.has_next():
                uuid, module_id = response.get_next()
                nodes.append({'uuid': uuid, 'module_id': module_id})
            return nodes
        except Exception as e:
            self.logger.error(f"self._execute_cypher(query, params): Error executing Cypher query: {str(e)}")
            return []

    def _find_module_by_id(self, module_id: str) -> Optional[Dict[str, Any]] | None:
        """Find a module by its ID

        Args:
            module_id: The module ID to find

        Returns:
            Module data or None if not found
        """
        try:
            query = """
            MATCH (m:CobolModule {module_id: $module_id})
            RETURN m.uuid AS uuid, m.module_id AS module_id
            """

            response = self.graph_db_connector.connection.execute(query)
            nodes = []
            while response.has_next():
                uuid, module_id = response.get_next()
                nodes.append({'uuid': uuid, 'module_id': module_id})
            if len(nodes) > 1:
                self.logger.warning(
                    f"self._find_module_by_id() returned more then 1 node for the same module_id {module_id}")
            return nodes[0] if len(nodes) > 0 else []
        except Exception as e:
            self.logger.error(f"self._find_paragraphs_in_module(module_id): Error executing Cypher query: {str(e)}")
            return None

    def _find_all_paragraphs(self):
        # Get all paragraphs in the module to find the range
        query = """
        MATCH (p)
        WHERE (p:CobolParagraph OR p:CobolEntryParagraph)
        AND p.module_id = $module_id
        RETURN p.uuid AS uuid, p.paragraph_name AS paragraph_name
        ORDER BY p.paragraph_name
        """

        # all_paragraphs = self._execute_cypher(query, module_id=module_id)
        pass

    def _find_paragraph_by_name(self, module_id: str, paragraph_name: str) -> Optional[Dict[str, Any]] | None:
        """Find a paragraph by its name in a specific module

        Args:
            module_id: The module ID
            paragraph_name: The paragraph name to find

        Returns:
            Paragraph data or None if not found
        """
        try:
            # Try exact match first
            query = """
            MATCH (p)
            WHERE p.module_id = $module_id
            AND p.paragraph_name = $paragraph_name
            AND (label(p) = 'CobolParagraph' OR label(p) = 'CobolEntryParagraph' OR label(p) = 'MissingCobolParagraph')
            RETURN label(p) AS type_label, p.uuid AS uuid, p.name AS name
            """

            response = self.graph_db_connector.connection.execute(query, {
                'module_id': module_id,
                'paragraph_name': paragraph_name
            })
            nodes = []
            while response.has_next():
                type_label, uuid, name = response.get_next()
                nodes.append({'uuid': uuid, 'type_label': type_label, 'name': name})

            if len(nodes) > 1:
                self.logger.warning(
                    f"self._find_paragraph_by_name() returned more then 1 node for the same module_id {module_id} and paragraph_name {paragraph_name}")
            return nodes[0] if nodes else None
        except Exception as e:
            self.logger.error(
                f"self._find_paragraph_by_name(module_id, paragraph_name): Error executing Cypher query: {str(e)}")
            return None

    def _find_paragraphs_in_module(self, module_id: str) -> List[Dict[str, Any]]:
        """Find all paragraphs in a specific module

        Args:
            module_id: The module ID

        Returns:
            List of paragraphs
        """
        try:
            query = """
            MATCH (p)
            WHERE p.module_id = $module_id
            AND (label(p) = 'CobolParagraph' OR label(p) = 'CobolEntryParagraph' OR label(p) = 'MissingCobolParagraph')
            RETURN label(p) as type_label, p.uuid AS uuid, p.name AS name, p.full_text AS full_text
            """

            response = self.graph_db_connector.connection.execute(query, {"module_id": module_id})
            nodes = []
            while response.has_next():
                type_label, uuid, name, full_text = response.get_next()
                nodes.append({'type_label': type_label, 'uuid': uuid, 'name': name, "full_text": full_text})
            return nodes
        except Exception as e:
            self.logger.error(f"self._find_paragraphs_in_module(module_id): Error executing Cypher query: {str(e)}")
            return []

    def _find_section_by_name(self, module_id: str, section_name: str) -> Optional[Dict[str, Any]] | None:
        """Find a section by its name in a specific module

        Args:
            module_id: The module ID
            section_name: The section name to find

        Returns:
            Section data or None if not found
            """
        try:
            # query = """
            # MATCH (s:CobolSection {module_id: $module_id, section_name: $section_name})
            # RETURN s.uuid AS uuid, s.section_name AS section_name
            # """
            query = """
            MATCH (p)
            WHERE p.module_id = $module_id
            AND label(p) = 'CobolSection'
            RETURN label(p) as type_label, p.uuid AS uuid, p.name AS name
            """

            response = self.graph_db_connector.connection.execute(query, {
                'module_id': module_id,
                'section_name': section_name
            })
            nodes = []
            while response.has_next():
                type_label, uuid, name = response.get_next()
                nodes.append({'uuid': uuid, 'type_label': type_label, 'name': name})
            if len(nodes) > 1:
                self.logger.warning(
                    f"self._find_paragraph_by_name() returned more then 1 node for the same module_id {module_id} and section_name {section_name}")
            return nodes[0] if nodes else None
        except Exception as e:
            self.logger.error(
                f"self._find_section_by_name(module_id, paragraph_name): Error executing Cypher query: {str(e)}")
            return None

    def _create_node(self, node_data: Dict[str, Any]) -> Optional[str]:
        """Create a node in graph database

        Args:
            node_data: Dictionary containing node data

        Returns:
            UUID of the created node or None if creation failed
        """
        try:
            node_type = node_data.get("type")
            if not node_type:
                self.logger.error("Node data missing type")
                return None

            # Create the node
            properties = {k: v for k, v in node_data.items() if k != "type"}

            # Execute the query
            return self._create_or_merge_if_exists(node_type, properties)

        except Exception as e:
            self.logger.error(f"Error creating node: {str(e)}")
            return None


    def _create_relationship(self, rel_data: Dict[str, Any]) -> Optional[str]:
        """Create a relationship in graph database

        Args:
            rel_data: Dictionary containing relationship data

        Returns:
            UUID of the created relationship or None if creation failed
        """
        try:
            if not rel_data or not rel_data['uuid'] or not rel_data['from_uuid'] or not rel_data['to_uuid']:
                self.logger.error("Required `rel_data` dictionary parameter missed.")
                return None

            from_type = rel_data.pop("from_type")
            to_type = rel_data.pop("to_type")
            rel_type = rel_data.pop("type")

            if not rel_type or not from_type or not to_type :
                self.logger.error("Required relationship fields `rel_type`, `from_type`, or `to_type` are missed")
                return None

            set_expression = ",".join([f"r.{key}=${key}" for key in rel_data.keys()])
            query = f"""
            MATCH (a:{from_type}), (b:{to_type})
            WHERE a.uuid = $from_uuid AND b.uuid = $to_uuid
            CREATE (a)-[r:{rel_type}]->(b)
            SET {set_expression}
            RETURN r.uuid AS uuid
            """

            # Execute the query
            response = self.graph_db_connector.connection.execute(query, rel_data)
            relations = []
            while response.has_next():
                uuid = response.get_next()
                relations.append({'uuid': uuid})

            return relations[0]["uuid"][0] if relations else None

        except Exception as e:
            self.logger.error(f"Error creating relationship: {str(e)}")
            return None

    def _create_or_merge_if_exists(self, node_type: str, props: Dict[str, Any]) -> Optional[str]:
        """

        """
        matching_fields = ['module_id', 'section_name', 'paragraph_name', 'variable_name']
        # noinspection PyBroadException
        lookup_expression = None
        update_set_expression = None
        create_set_expression = None
        # noinspection PyBroadException
        try:
            return_expression = "RETURN n.uuid;"
            matching_fields = [matching_field for matching_field in matching_fields if matching_field in props.keys()]
            matching_expressions = [f"n.{matching_field}=${matching_field}" for matching_field in matching_fields]
            where_clause = f" WHERE {' AND '.join(matching_expressions).strip()}" if matching_expressions else ""
            match_expression = f"MATCH (n:{node_type}){where_clause}"

            """
            Lookup expression, e.g.:
            MATCH (n:CobolModule) WHERE n.module_id = $module_id 
            RETURN n.uuid
            """
            lookup_expression = match_expression + '\n' + return_expression
            lookup_parameters = {key: value for key, value in props.items() if key in matching_fields}

            """
            Set expression, e.g.:
            CREATE (n:CobolModule {uuid: $uuid, module_id: $module_id, file_name: $file_name, full_text: $full_text}) 
            RETURN n.uuid
            """
            all_fields = [key for key, value in props.items()]
            create_set_expressions = [f"{field}:${field}" for field in all_fields]
            create_set_expression = f"CREATE (n:{node_type} {{{','.join(create_set_expressions).strip()}}})"
            create_set_expression = create_set_expression + '\n' + return_expression
            create_parameters = {**props}

            """ 
            MATCH (n:CobolModule) WHERE n.module_id = $module_id SET n.file_name=$file_name, n.full_text=$full_text 
            RETURN n.uuid
            """
            update_fields = [field for field in all_fields if field != "uuid"
                             if field not in matching_fields and field != "uuid"]
            update_set_expressions = [f"n.{field}=${field}" for field in update_fields]
            update_set_expression = f"SET " + ",".join(update_set_expressions).strip()
            update_set_expression = match_expression + '\n' + update_set_expression + '\n' + return_expression
            update_parameters = {key: value for key, value in props.items() if key in update_fields}
            update_parameters = {**lookup_parameters, **update_parameters}

            # Step 1: Try to find an existing node
            result = self.graph_db_connector.connection.execute(lookup_expression, lookup_parameters)
            if result.has_next():
                # Step 2a: Update an existing node
                response = self.graph_db_connector.connection.execute(update_set_expression, update_parameters)
                nodes = []
                while response.has_next():
                    uuid = response.get_next()
                    nodes.append({'uuid': uuid})
                return nodes[0]['uuid'][0] if nodes else None
            else:
                # Step 2b: Create a new node
                response = self.graph_db_connector.connection.execute(create_set_expression, create_parameters)
                nodes = []
                while response.has_next():
                    uuid = response.get_next()
                    nodes.append({'uuid': uuid})
                return nodes[0]['uuid'][0] if nodes else None

        except Exception as e:
            expression_report = lookup_expression if lookup_expression else (
                update_set_expression if update_set_expression else create_set_expression)
            self.logger.error(
                f"Create or update of n:{node_type} failed while generating CYPHER query {expression_report}. Exception {str(e)}")
            return None
