from typing import Dict, Any, Optional

from .base_visitor import BaseVisitor

class ParagraphVisitor(BaseVisitor):
    """Visitor for processing COBOL paragraphs"""
    
    def visit_cobol_paragraph(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Visit a CobolParagraph node
        
        Args:
            node: The CobolParagraph node
            
        Returns:
            The processed node
        """
        self.logger.debug(f"Visiting Paragraph: {node.get('paragraph_name')}")
        
        return node
    
    def visit_cobol_entry_paragraph(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Visit a CobolEntryParagraph node
        
        Args:
            node: The CobolEntryParagraph node
            
        Returns:
            The processed node
        """
        self.logger.debug(f"Visiting Entry Paragraph: {node.get('paragraph_name')}, "
                      f"Entry: {node.get('entry_name')}, "
                      f"Main: {node.get('is_main_entry')}")
        
        return node
