import re
from typing import Dict, Any, <PERSON>, Tuple

from src.platform.tools.data.kuzu import KuzuConnector
from ..models.nodes import MissingCobolParagraph
from ..models.relationships import GotoRelationship
from .base import BaseAnalyzer


## TODO Refactoring: we should extract all database operation from here to a Data layer abstract class
##  and implement them behind that class, implementing concrete DB connector classes
class GotoAnalyzer(BaseAnalyzer):
    """Analyzer for GOTO statements in COBOL code"""

    def __init__(self, graph_db_connector: KuzuConnector):
        """Initialize the GOTO statement analyzer

        Args:
            graph_db_connector: graph database connector instance
        """
        super().__init__(graph_db_connector)

        # Regular expression for GOTO statements
        self.goto_pattern = re.compile(
            r'(?i)GO\s+TO\s+([A-Za-z0-9_-]+)'
        )

    def analyze(self) -> Tuple[int, int]:
        """Analyze GOTO statements in COBOL code

        Returns:
            Tuple of (nodes_created, relationships_created)
        """
        self.logger.info("Starting GOTO statement analysis")

        # Get all modules
        modules = self._get_all_modules()
        self.logger.info(f"Found {len(modules)} modules to analyze")

        nodes_created = 0
        relationships_created = 0

        # Process each module
        for module in modules:
            module_id = module["module_id"]

            self.logger.info(f"Processing module: {module_id}")

            # Get all paragraphs in the module
            paragraphs = self._find_paragraphs_in_module(module_id)
            self.logger.info(f"Found {len(paragraphs)} paragraphs in module {module_id}")

            # Process each paragraph
            for paragraph in paragraphs:

                if not paragraph['full_text']:
                    self.logger.warning(f"Paragraph {paragraph['name']} in {module_id} has no text")
                    continue

                # Process GOTO statements
                goto_targets = self._find_goto_targets(paragraph['full_text'])
                for goto_target in goto_targets:
                    target_name = goto_target["target"]
                    goto_text = goto_target["text"]

                    # Find the target paragraph
                    target_paragraph = self._find_paragraph_by_name(module_id, target_name)

                    # If target paragraph doesn't exist and is not a COBOL keyword, create a missing paragraph node
                    if not target_paragraph and not MissingCobolParagraph.is_keyword(target_name):
                        missing_paragraph = MissingCobolParagraph(module_id, target_name)
                        missing_paragraph_dict = missing_paragraph.to_dict()
                        missing_paragraph_uuid = self._create_node(missing_paragraph_dict)

                        if missing_paragraph_uuid:
                            target_paragraph = {
                                "uuid": missing_paragraph_uuid,
                                "name": target_name,
                                "type_label": "MissingCobolParagraph"
                            }
                            nodes_created += 1

                    # Create GOTO relationship
                    if target_paragraph:
                        goto_rel = GotoRelationship(
                            from_uuid=paragraph["uuid"],
                            from_type=paragraph["type_label"],
                            to_uuid=target_paragraph["uuid"],
                            to_type=target_paragraph["type_label"],
                            goto_text=goto_text
                        )
                        goto_rel_dict = goto_rel.to_dict()
                        if self._create_relationship(goto_rel_dict):
                            relationships_created += 1

        self.logger.info(
            f"GOTO analysis completed: Created {nodes_created} nodes and {relationships_created} relationships")
        return nodes_created, relationships_created

    def _get_all_modules(self) -> List[Dict[str, Any]]:
        """Get all COBOL modules in the database

        Returns:
            List of modules
        """
        try:
            query = """
            MATCH (m:CobolModule)
            RETURN m.uuid AS uuid, m.module_id AS module_id
            """
            response = self.graph_db_connector.connection.execute(query)
            nodes = []
            while response.has_next():
                uuid, module_id = response.get_next()
                nodes.append({'uuid': uuid, 'module_id': module_id})
            return nodes
        except Exception as e:
            self.logger.error(f"self._get_all_modules(): Error executing Cypher query: {str(e)}")
            return []

    def _find_goto_targets(self, text: str) -> List[Dict[str, str]]:
        """Find GOTO targets in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The target paragraph name
            - text: The full text of the GOTO statement
        """
        results = []

        # Find all GOTO statements
        for match in self.goto_pattern.finditer(text):
            target = match.group(1).strip()
            goto_text = match.group(0).strip()

            results.append({
                "target": target,
                "text": goto_text
            })

        return results
