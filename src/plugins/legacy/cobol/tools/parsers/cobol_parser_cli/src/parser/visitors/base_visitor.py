import logging
from typing import Dict, List, Any, Optional

class BaseVisitor:
    """Base visitor for traversing the COBOL AST"""
    
    def __init__(self):
        """Initialize the base visitor"""
        self.logger = logging.getLogger(__name__)
    
    def visit(self, node: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Visit an AST node
        
        Args:
            node: The AST node to visit
            
        Returns:
            The processed node or None
        """
        # Use the node type to determine which method to call
        node_type = node.get("type")
        if not node_type:
            self.logger.warning(f"Node has no type: {node}")
            return None
        
        # Convert CamelCase to snake_case for method name
        method_name = f"visit_{''.join('_' + c.lower() if c.isupper() else c.lower() for c in node_type)}"
        
        # Remove the leading underscore
        if method_name.startswith('_'):
            method_name = method_name[1:]
        
        # Call the corresponding visit method
        if hasattr(self, method_name):
            return getattr(self, method_name)(node)
        else:
            self.logger.debug(f"No visit method for {node_type}, using generic visit")
            return self.generic_visit(node) # TODO ?? Recursion isn't good - potential stack-overflow
    
    def generic_visit(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Generic visitor for nodes without specific visit methods
        
        Args:
            node: The AST node to visit
            
        Returns:
            The processed node
        """
        children = node.get("children", [])
        if children:
            # Visit all children and replace them
            new_children = []
            for child in children:
                visited_child = self.visit(child) # TODO ?? Recursion isn't good - potential stack-overflow
                if visited_child is not None:
                    new_children.append(visited_child)
            
            node["children"] = new_children
        
        return node
