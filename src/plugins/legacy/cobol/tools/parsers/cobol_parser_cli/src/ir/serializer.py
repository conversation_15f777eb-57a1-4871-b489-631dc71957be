import os
import json
import yaml
import logging
from typing import Dict, Any, Optional

class IRSerializer:
    """Serializes intermediate representation to various formats"""
    
    def __init__(self):
        """Initialize the IR serializer"""
        self.logger = logging.getLogger(__name__)
    
    def to_json(self, ir: Dict[str, Any], output_path: str) -> bool:
        """Serialize the intermediate representation to JSON
        
        Args:
            ir: The intermediate representation
            output_path: Path to the output file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump(ir, f, indent=2)
            
            self.logger.info(f"IR serialized to JSON: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to serialize IR to JSON: {str(e)}")
            return False
    
    def to_yaml(self, ir: Dict[str, Any], output_path: str) -> bool:
        """Serialize the intermediate representation to YAML
        
        Args:
            ir: The intermediate representation
            output_path: Path to the output file
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w') as f:
                yaml.dump(ir, f, default_flow_style=False)
            
            self.logger.info(f"IR serialized to YAML: {output_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to serialize IR to YAML: {str(e)}")
            return False
    
    def from_json(self, input_path: str) -> Optional[Dict[str, Any]]:
        """Deserialize the intermediate representation from JSON
        
        Args:
            input_path: Path to the input file
            
        Returns:
            The deserialized intermediate representation or None if failed
        """
        try:
            with open(input_path, 'r') as f:
                ir = json.load(f)
            
            self.logger.info(f"IR deserialized from JSON: {input_path}")
            return ir
        except Exception as e:
            self.logger.error(f"Failed to deserialize IR from JSON: {str(e)}")
            return None
    
    def from_yaml(self, input_path: str) -> Optional[Dict[str, Any]]:
        """Deserialize the intermediate representation from YAML
        
        Args:
            input_path: Path to the input file
            
        Returns:
            The deserialized intermediate representation or None if failed
        """
        try:
            with open(input_path, 'r') as f:
                ir = yaml.safe_load(f)
            
            self.logger.info(f"IR deserialized from YAML: {input_path}")
            return ir
        except Exception as e:
            self.logger.error(f"Failed to deserialize IR from YAML: {str(e)}")
            return None
