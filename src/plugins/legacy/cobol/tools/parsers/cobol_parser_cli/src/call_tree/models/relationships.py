import uuid
from typing import Dict, Any, Optional


class RelationshipBase:
    """Base class for all call tree relationships"""

    def __init__(self):
        """Initialize the base relationship with a UUID"""
        self.uuid = str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        raise NotImplementedError("Subclass must implement to_dict()")


class CallsRelationship(RelationshipBase):
    """Represents a CALLS relationship between paragraphs"""

    def __init__(self, from_uuid: str, from_type: str, to_uuid: str, to_type: str, call_text: str,
                 is_dynamic: bool = False):
        """Initialize a CALLS relationship
        
        Args:
            from_uuid: UUID of the source node
            to_uuid: UUID of the target node
            call_text: The full text of the CALL statement
            is_dynamic: Boolean flag indicating if this is a dynamic CALL
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type
        self.call_text = call_text
        self.is_dynamic = is_dynamic

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        return {
            "type": "CALLS",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type,
            "call_text": self.call_text,
            "is_dynamic": self.is_dynamic
        }


class ModuleCallsRelationship(RelationshipBase):
    """Represents a MODULE_CALLS relationship between modules"""

    def __init__(self, from_uuid: str, from_type: str, to_uuid: str, to_type: str):
        """Initialize a MODULE_CALLS relationship
        
        Args:
            from_uuid: UUID of the source module
            to_uuid: UUID of the target module
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        return {
            "type": "MODULE_CALLS",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type
        }


class DynamicCallsRelationship(RelationshipBase):
    """Represents a DYNAMIC_CALLS relationship"""

    def __init__(self, from_uuid: str, from_type: str, to_uuid: str, to_type: str):
        """Initialize a DYNAMIC_CALLS relationship
        
        Args:
            from_uuid: UUID of the source module
            to_uuid: UUID of the target variable
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        return {
            "type": "DYNAMIC_CALLS",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type
        }


class GotoRelationship(RelationshipBase):
    """Represents a GOTO relationship between paragraphs"""

    def __init__(self, from_uuid: str, from_type: str, to_uuid: str, to_type: str, goto_text: str):
        """Initialize a GOTO relationship
        
        Args:
            from_uuid: UUID of the source paragraph
            to_uuid: UUID of the target paragraph
            goto_text: The full text of the GOTO statement
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type
        self.goto_text = goto_text

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        return {
            "type": "GOTO",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type,
            "goto_text": self.goto_text
        }


class PerformsRelationship(RelationshipBase):
    """Represents a PERFORMS relationship between paragraphs or sections"""

    def __init__(
            self,
            from_uuid: str,
            from_type: str,
            to_uuid: str,
            to_type: str,
            perform_text: str,
            is_conditional: bool = False,
            condition_type: Optional[str] = None,
            condition_text: Optional[str] = None,
            iteration_count: Optional[str] = None,
            loop_variable: Optional[str] = None,
            start_value: Optional[str] = None,
            increment_value: Optional[str] = None,
            loop_structure: Optional[Dict[str, Any]] = None
    ):
        """Initialize a PERFORMS relationship
        
        Args:
            from_uuid: UUID of the source paragraph
            to_uuid: UUID of the target paragraph or section
            perform_text: The full text of the PERFORM statement
            is_conditional: Boolean flag indicating if this is a conditional PERFORM
            condition_type: Type of condition (TIMES, UNTIL, VARYING, VARYING_NESTED)
            condition_text: The text of the condition
            iteration_count: For TIMES conditions, the value or variable name
            loop_variable: For VARYING conditions, the variable being varied
            start_value: For VARYING conditions, the starting value
            increment_value: For VARYING conditions, the increment value
            loop_structure: For nested VARYING conditions, JSON representation of loop structure
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type
        self.perform_text = perform_text
        self.is_conditional = is_conditional
        self.condition_type = condition_type
        self.condition_text = condition_text
        self.iteration_count = iteration_count
        self.loop_variable = loop_variable
        self.start_value = start_value
        self.increment_value = increment_value
        self.loop_structure = loop_structure

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        result = {
            "type": "PERFORMS",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type,
            "perform_text": self.perform_text,
            "is_conditional": self.is_conditional
        }

        # Add optional properties only if they are set
        if self.condition_type:
            result["condition_type"] = self.condition_type

        if self.condition_text:
            result["condition_text"] = self.condition_text

        if self.iteration_count:
            result["iteration_count"] = self.iteration_count

        if self.loop_variable:
            result["loop_variable"] = self.loop_variable

        if self.start_value:
            result["start_value"] = self.start_value

        if self.increment_value:
            result["increment_value"] = self.increment_value

        if self.loop_structure:
            result["loop_structure"] = self.loop_structure

        return result


class CicsRelationship(RelationshipBase):
    """Represents a CICS relationship between a paragraph and a CICS command"""

    def __init__(self, from_uuid: str, from_type: str, to_uuid: str, to_type: str):
        """Initialize a CICS relationship
        
        Args:
            from_uuid: UUID of the source paragraph
            to_uuid: UUID of the target CICS command
        """
        super().__init__()
        self.from_uuid = from_uuid
        self.from_type = from_type
        self.to_uuid = to_uuid
        self.to_type = to_type

    def to_dict(self) -> Dict[str, Any]:
        """Convert the relationship to a dictionary representation
        
        Returns:
            Dictionary representation of the relationship
        """
        return {
            "type": "CICS",
            "uuid": self.uuid,
            "from_uuid": self.from_uuid,
            "from_type": self.from_type,
            "to_uuid": self.to_uuid,
            "to_type": self.to_type
        }
