import re
from typing import Dict, Any, List, Optional, Tuple

from src.platform.tools.data.kuzu import KuzuConnector
from ..models.nodes import MissingCobolParagraph
from ..models.relationships import PerformsRelationship
from .base import BaseAnalyzer


## TODO Refactoring: we should extract all database operation from here to a Data layer abstract class
##  and implement them behind that class, implementing concrete DB connector classes
class PerformAnalyzer(BaseAnalyzer):
    """Analyzer for PERFORM statements in COBOL code"""

    def __init__(self, graph_db_connector: KuzuConnector):
        """Initialize the PERFORM statement analyzer

        Args:
            graph_db_connector: graph database connector instance

        """
        super().__init__(graph_db_connector)

        # COBOL keywords that should not be treated as paragraph names
        self.cobol_keywords = {"ACCEPT", "ADD", "ALTER", "CALL", "CANCEL", "CLOSE", "COMPUTE", "CONTINUE", "DELETE",
                               "DISPLAY", "DIVIDE", "ELSE", "END", "END-IF", "END-PERFORM", "EVALUATE", "EXIT", "GO",
                               "GOBACK", "IF", "INITIALIZE", "INSPECT", "MOVE", "MULTIPLY", "NEXT", "OPEN", "PERFORM",
                               "READ", "REWRITE", "SEARCH", "SET", "STOP", "STRING", "SUBTRACT", "UNSTRING", "UNTIL",
                               "VARYING", "WRITE", "EXEC"}

        # Regular expressions for PERFORM statements

        # Basic PERFORM paragraph
        self.basic_perform_pattern = re.compile(
            r'(?i)PERFORM\s+([A-Za-z0-9_-]+)\s*\.?'
        )

        # PERFORM THROUGH/THRU
        self.perform_through_pattern = re.compile(
            r'(?i)PERFORM\s+([A-Za-z0-9_-]+)\s+(?:THROUGH|THRU)\s+([A-Za-z0-9_-]+)'
        )

        # PERFORM paragraph n TIMES
        self.perform_times_pattern = re.compile(
            r'(?i)PERFORM\s+([A-Za-z0-9_-]+)\s+(.*?)\s+TIMES'
        )

        # PERFORM paragraph UNTIL condition
        self.perform_until_pattern = re.compile(
            r'(?i)PERFORM\s+([A-Za-z0-9_-]+)\s+UNTIL\s+(.*?)(?:\s+END-PERFORM|\s*\.)'
        )

        # PERFORM paragraph VARYING var FROM x BY y UNTIL condition
        self.perform_varying_pattern = re.compile(
            r'(?i)PERFORM\s+([A-Za-z0-9_-]+)\s+VARYING\s+([A-Za-z0-9_-]+)\s+FROM\s+(.*?)\s+BY\s+(.*?)\s+UNTIL\s+(.*?)(?:\s+END-PERFORM|\s*\.)'
        )

        # Inline PERFORM patterns (for filtering)
        self.inline_perform_pattern = re.compile(
            r'(?i)PERFORM\s+(?:UNTIL|VARYING|WITH TEST)'
        )

        # Pattern to detect PERFORM with no target (just PERFORM)
        self.bare_perform_pattern = re.compile(
            r'(?i)PERFORM\s*(?:$|\.)'
        )

    def analyze(self) -> Tuple[int, int]:
        """Analyze PERFORM statements in COBOL code

        There are 4 PERFORM relation pairs possible:
                FROM CobolEntryParagraph TO CobolParagraph,
                FROM CobolEntryParagraph TO CobolSection,
                FROM CobolParagraph TO CobolSection,
                FROM CobolParagraph TO CobolParagraph

        Key differences of PERFORMING Section vs. Paragraph:

        Scope: When you PERFORM a section, it executes ALL paragraphs within that section until it hits another section
                or the end of the division.
        Control flow: PERFORMing a section executes from the first paragraph in the section until the section ends,
                regardless of individual paragraph EXIT statements.
        Best practice: Most modern COBOL code prefers PERFORMing individual paragraphs for better control and readability.

        Returns:
            Tuple of (nodes_created, relationships_created)
        """
        self.logger.info("Starting PERFORM statement analysis") ## TODO ?? Add try catch ??

        # Get all modules
        modules = self._get_all_modules()
        self.logger.info(f"Found {len(modules)} modules to analyze")

        nodes_created = 0
        relationships_created = 0

        # Process each module
        for module in modules:
            module_id = module["module_id"]

            self.logger.info(f"Processing module: {module_id}")

            # Get all paragraphs in the module
            paragraphs = self._find_paragraphs_in_module(module_id)
            self.logger.info(f"Found {len(paragraphs)} paragraphs in module {module_id}")

            # Process each paragraph
            for paragraph in paragraphs:

                if not paragraph['full_text']:
                    self.logger.warning(f"Paragraph {paragraph['name']} in {module_id} has no text")
                    continue

                # Don't skip paragraphs with inline PERFORM statements
                # Process basic PERFORM statements even if there are inline PERFORMs

                basic_performs = self._find_basic_performs(paragraph['full_text'])
                for perform in basic_performs:

                    # Find the target paragraph or section
                    target = self._find_target_by_name(module_id, perform["target"])

                    # If target doesn't exist and is not a COBOL keyword, create a missing paragraph node
                    if not target and not MissingCobolParagraph.is_keyword(perform["target"]):
                        missing_paragraph = MissingCobolParagraph(module_id, perform["target"])
                        missing_paragraph_dict = missing_paragraph.to_dict()
                        missing_paragraph_uuid = self._create_node(missing_paragraph_dict)

                        if missing_paragraph_uuid:
                            target = {"uuid": missing_paragraph_uuid, "name": perform["target"]}
                            nodes_created += 1

                    # Create a PERFORMS relationship
                    if target:
                        performs_rel = PerformsRelationship(
                            from_uuid=paragraph['uuid'],
                            from_type=paragraph['type_label'],
                            to_uuid=target["uuid"],
                            to_type=target['type_label'],
                            perform_text=perform["text"],
                            is_conditional=False
                        )
                        performs_rel_dict = performs_rel.to_dict()
                        if self._create_relationship(performs_rel_dict):
                            relationships_created += 1

                # Process PERFORM THROUGH/THRU statements
                through_performs = self._find_through_performs(paragraph['full_text'])
                for perform in through_performs:
                    start_name = perform["start"]
                    end_name = perform["end"]
                    perform_text = perform["text"]

                    # Create relationships for all paragraphs in the range
                    success = self._create_perform_through_relationships(
                        module_id, paragraph, start_name, end_name, perform["text"]
                    )

                    if success:
                        # Count relationship creation based on the number of paragraphs in the range
                        range_paragraphs = self._count_paragraphs_in_range(module_id, start_name, end_name)
                        relationships_created += range_paragraphs

                # Process PERFORM TIMES statements
                times_performs = self._find_times_performs(paragraph['full_text'])
                for perform in times_performs:

                    # Find the target paragraph or section
                    target = self._find_target_by_name(module_id, perform["target"])

                    # If target doesn't exist and is not a COBOL keyword, create a missing paragraph node
                    if not target and not MissingCobolParagraph.is_keyword(perform["target"]):
                        missing_paragraph = MissingCobolParagraph(module_id, perform["target"])
                        missing_paragraph_dict = missing_paragraph.to_dict()
                        missing_paragraph_uuid = self._create_node(missing_paragraph_dict)

                        if missing_paragraph_uuid:
                            target = {"uuid": missing_paragraph_uuid, "name": perform["target"]}
                            nodes_created += 1

                    # Create PERFORMS relationship with TIMES condition
                    if target:
                        performs_rel = PerformsRelationship(
                            from_uuid=paragraph['uuid'],
                            from_type=paragraph['type_label'],
                            to_uuid=target["uuid"],
                            to_type=target["type_label"],
                            perform_text=perform["text"],
                            is_conditional=True,
                            condition_type="TIMES",
                            condition_text=f"{perform['times']} TIMES",
                            iteration_count=perform["times"]
                        )
                        performs_rel_dict = performs_rel.to_dict()
                        if self._create_relationship(performs_rel_dict):
                            relationships_created += 1

                # Process PERFORM UNTIL statements
                until_performs = self._find_until_performs(paragraph['full_text'])
                for perform in until_performs:
                    target_name = perform["target"]
                    condition = perform["condition"]
                    perform_text = perform["text"]

                    # Find the target paragraph or section
                    target = self._find_target_by_name(module_id, target_name)

                    # If target doesn't exist and is not a COBOL keyword, create a missing paragraph node
                    if not target and not MissingCobolParagraph.is_keyword(target_name):
                        missing_paragraph = MissingCobolParagraph(module_id, target_name)
                        missing_paragraph_dict = missing_paragraph.to_dict()
                        missing_paragraph_uuid = self._create_node(missing_paragraph_dict)

                        if missing_paragraph_uuid:
                            target = {"uuid": missing_paragraph_uuid, "name": target_name}
                            nodes_created += 1

                    # Create PERFORMS relationship with UNTIL condition
                    if target:
                        performs_rel = PerformsRelationship(
                            from_uuid=paragraph['uuid'],
                            from_type=paragraph['type_label'],
                            to_uuid=target["uuid"],
                            to_type=target["type_label"],
                            perform_text=perform["text"],
                            is_conditional=True,
                            condition_type="UNTIL",
                            condition_text=f"UNTIL {condition}"
                        )
                        performs_rel_dict = performs_rel.to_dict()
                        if self._create_relationship(performs_rel_dict):
                            relationships_created += 1

                # Process PERFORM VARYING statements
                varying_performs = self._find_varying_performs(paragraph['full_text'])
                for perform in varying_performs:
                    target_name = perform["target"]
                    loop_var = perform["variable"]
                    start_val = perform["start"]
                    increment = perform["increment"]
                    condition = perform["condition"]
                    perform_text = perform["text"]

                    # Find the target paragraph or section
                    target = self._find_target_by_name(module_id, target_name)

                    # If target doesn't exist and is not a COBOL keyword, create a missing paragraph node
                    if not target and not MissingCobolParagraph.is_keyword(target_name):
                        missing_paragraph = MissingCobolParagraph(module_id, target_name)
                        missing_paragraph_dict = missing_paragraph.to_dict()
                        missing_paragraph_uuid = self._create_node(missing_paragraph_dict)

                        if missing_paragraph_uuid:
                            target = {"uuid": missing_paragraph_uuid, "name": target_name}
                            nodes_created += 1

                    # Create PERFORMS relationship with VARYING condition
                    if target:
                        performs_rel = PerformsRelationship(
                            from_uuid=paragraph['uuid'],
                            from_type=paragraph['type_label'],
                            to_uuid=target["uuid"],
                            to_type=target['type_label'],
                            perform_text=perform["text"],
                            is_conditional=True,
                            condition_type="VARYING",
                            condition_text=f"VARYING {loop_var} FROM {start_val} BY {increment} UNTIL {condition}",
                            loop_variable=loop_var,
                            start_value=start_val,
                            increment_value=increment
                        )
                        performs_rel_dict = performs_rel.to_dict()
                        if self._create_relationship(performs_rel_dict):
                            relationships_created += 1

        self.logger.info(
            f"PERFORM analysis completed: Created {nodes_created} nodes and {relationships_created} relationships")
        return nodes_created, relationships_created

    def _get_all_modules(self) -> List[Dict[str, Any]]:
        """Get all COBOL modules in the database

        Returns:
            List of modules
        """
        query = """
        MATCH (m:CobolModule)
        RETURN m.uuid AS uuid, m.module_id AS module_id
        """

        return self._execute_cypher(query)

    def _find_target_by_name(self, module_id: str, target_name: str) -> Optional[Dict[str, Any]]:
        """Find a target (paragraph or section) by name

        Args:
            module_id: The module ID
            target_name: The target name to find

        Returns:
            Target data or None if not found
        """
        # Try to find as paragraph first
        paragraph = self._find_paragraph_by_name(module_id, target_name)
        if paragraph:
            return {"uuid": paragraph["uuid"], "name": paragraph["name"], "type_label": paragraph["type_label"]}

        # If not found, try to find as section
        section = self._find_section_by_name(module_id, target_name)
        if section:
            return {"uuid": section["uuid"], "name": section["name"], "type_label": section["type_label"]}

        return None

    def _find_basic_performs(self, text: str) -> List[Dict[str, str]]:
        """Find basic PERFORM statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The target paragraph or section name
            - text: The full text of the PERFORM statement
        """
        results = []

        # Don't skip paragraphs with inline PERFORM statements
        # Find all basic PERFORM statements
        for match in self.basic_perform_pattern.finditer(text):
            target = match.group(1).strip()
            perform_text = match.group(0).strip()

            # Skip if this is part of a more complex PERFORM pattern
            # if any(p in perform_text.upper() for p in ["THROUGH", "THRU", "TIMES", "UNTIL", "VARYING"]):
            #     continue
            ## TODO THRU Refactoring: Don't skip, because the relation is anyway PERFORMS, doesn't matter how many
            ##  time or until what it goes. The only exception is THROUGH/THRU as it can be a multiple PERFORM call
            ##  But we skip it for now as our current program doesn't have multiple call PERFORM constructs

            # Skip if the target is a COBOL keyword
            if target.upper() in self.cobol_keywords:
                continue

            results.append({
                "target": target,
                "text": perform_text
            })

        return results

    def _find_through_performs(self, text: str) -> List[Dict[str, str]]:
        """Find PERFORM THROUGH statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - start: The start paragraph name
            - end: The end paragraph name
            - text: The full text of the PERFORM statement
        """
        results = []
        # Find all PERFORM THROUGH statements
        # for match in self.perform_through_pattern.finditer(text):
        #     start = match.group(1).strip()
        #     end = match.group(2).strip()
        #     perform_text = match.group(0).strip()
        #
        #     # Skip if either target is a COBOL keyword
        #     if start.upper() in self.cobol_keywords or end.upper() in self.cobol_keywords:
        #         continue
        #
        #     results.append({
        #         "start": start,
        #         "end": end,
        #         "text": perform_text
        #     })
        return results ## TODO Switch off THRU analysis temporary as it's broken (It's OK for currently analyzed program)

    def _find_times_performs(self, text: str) -> List[Dict[str, str]]:
        """Find PERFORM TIMES statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The target paragraph name
            - times: The number of times or variable
            - text: The full text of the PERFORM statement
        """
        results = []
        # Find all PERFORM TIMES statements
        # for match in self.perform_times_pattern.finditer(text):
        #     target = match.group(1).strip()
        #     times = match.group(2).strip()
        #     perform_text = match.group(0).strip()
        #
        #     # Skip if the target is a COBOL keyword
        #     if target.upper() in self.cobol_keywords:
        #         continue
        #
        #     results.append({
        #         "target": target,
        #         "times": times,
        #         "text": perform_text
        #     })
        return results ## TODO Switch off TIMES analysis as it's actually just a PERFORM relation

    def _find_until_performs(self, text: str) -> List[Dict[str, str]]:
        """Find PERFORM UNTIL statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The target paragraph name
            - condition: The UNTIL condition
            - text: The full text of the PERFORM statement
        """
        results = []
        # Find all PERFORM UNTIL statements
        # for match in self.perform_until_pattern.finditer(text):
        #     target = match.group(1).strip()
        #     condition = match.group(2).strip()
        #     perform_text = match.group(0).strip()
        #
        #     # Skip if the target is a COBOL keyword
        #     if target.upper() in self.cobol_keywords:
        #         continue
        #
        #     results.append({
        #         "target": target,
        #         "condition": condition,
        #         "text": perform_text
        #     })
        return results ## TODO Switch off UNTIL analysis as it's actually just a PERFORM relation

    def _find_varying_performs(self, text: str) -> List[Dict[str, str]]:
        """Find PERFORM VARYING statements in text

        Args:
            text: The text to search

        Returns:
            List of dictionaries containing:
            - target: The target paragraph name
            - variable: The loop variable
            - start: The start value
            - increment: The increment value
            - condition: The UNTIL condition
            - text: The full text of the PERFORM statement
        """
        results = []
        # Find all PERFORM VARYING statements
        # for match in self.perform_varying_pattern.finditer(text):
        #     target = match.group(1).strip()
        #     variable = match.group(2).strip()
        #     start = match.group(3).strip()
        #     increment = match.group(4).strip()
        #     condition = match.group(5).strip()
        #     perform_text = match.group(0).strip()
        #
        #     # Skip if the target is a COBOL keyword
        #     if target.upper() in self.cobol_keywords:
        #         continue
        #
        #     results.append({
        #         "target": target,
        #         "variable": variable,
        #         "start": start,
        #         "increment": increment,
        #         "condition": condition,
        #         "text": perform_text
        #     })
        return results ## TODO Switch off VARYING analysis as it's actually just a PERFORM relation

    def _create_perform_through_relationships(
            self, module_id: str, from_paragraph: Dict[str, Any], start_name: str, end_name: str, perform_text: str
    ) -> bool:
        """Create PERFORMS relationships for all paragraphs in a range

        Args:
            module_id: The module ID
            from_paragraph: The source paragraph
            start_name: Name of the start paragraph
            end_name: Name of the end paragraph
            perform_text: The full text of the PERFORM statement

        Returns:
            True if relationships were created, False otherwise
        """
        relationships_created = 0
        # Get all paragraphs in the module to find the range
        all_paragraphs = self._find_paragraphs_in_module(module_id)

        # Find the range of paragraphs between start_name and end_name
        in_range = False
        range_paragraphs = []

        for paragraph in all_paragraphs:

            if paragraph["name"] == start_name:
                in_range = True

            if in_range:
                range_paragraphs.append(paragraph)

            if paragraph["name"] == end_name:
                in_range = False
                break

        # If we didn't find a proper range, create relationships to missing paragraphs
        if not range_paragraphs:
            # Create missing paragraph nodes if needed, but only if they're not COBOL keywords
            start_paragraph = self._find_paragraph_by_name(module_id, start_name)
            if not start_paragraph and start_name.upper() not in self.cobol_keywords:
                missing_start = MissingCobolParagraph(module_id, start_name)
                missing_start_dict = missing_start.to_dict()
                start_uuid = self._create_node(missing_start_dict)
                if start_uuid:
                    start_paragraph = {"uuid": start_uuid, "name": start_name}

            end_paragraph = self._find_paragraph_by_name(module_id, end_name)
            if not end_paragraph and end_name.upper() not in self.cobol_keywords:
                missing_end = MissingCobolParagraph(module_id, end_name)
                missing_end_dict = missing_end.to_dict()
                end_uuid = self._create_node(missing_end_dict)
                if end_uuid:
                    end_paragraph = {"uuid": end_uuid, "name": end_name}

            # Create relationships to the start and end paragraphs
            if start_paragraph:
                performs_rel = PerformsRelationship(
                    from_uuid=from_paragraph['uuid'],
                    from_type=from_paragraph['type_label'],
                    to_uuid=start_paragraph["uuid"],
                    to_type=start_paragraph['type_label'],
                    perform_text=perform_text,
                    is_conditional=False
                )
                if self._create_relationship(performs_rel.to_dict()):
                    relationships_created += 1

            if end_paragraph and end_paragraph != start_paragraph:
                performs_rel = PerformsRelationship(
                    from_uuid=from_paragraph['uuid'],
                    from_type=from_paragraph['type_label'],
                    to_uuid=end_paragraph["uuid"],
                    to_type=end_paragraph['type_label'],
                    perform_text=perform_text,
                    is_conditional=False
                )
                if self._create_relationship(performs_rel.to_dict()):
                    relationships_created += 1

            return start_paragraph is not None or end_paragraph is not None

        # Create relationships to all paragraphs in the range
        for paragraph in range_paragraphs:

            performs_rel = PerformsRelationship(
                from_uuid=from_paragraph['uuid'],
                from_type=from_paragraph['type_label'],
                to_uuid=paragraph["uuid"],
                to_type=paragraph["type_label"],
                perform_text=perform_text,
                is_conditional=False
            )
            if self._create_relationship(performs_rel.to_dict()):
                relationships_created += 1

        return len(range_paragraphs) > 0

    def _count_paragraphs_in_range(self, module_id: str, start_name: str, end_name: str) -> int:
        """Count paragraphs in a range for a module

        Args:
            module_id: The module ID
            start_name: Name of the start paragraph
            end_name: Name of the end paragraph

        Returns:
            Number of paragraphs in the range
        """
        # Get all paragraphs in the module
        query = """
        MATCH (p)
        WHERE (p:CobolParagraph OR p:CobolEntryParagraph)
        AND p.module_id = $module_id
        RETURN p.paragraph_name AS paragraph_name
        ORDER BY p.paragraph_name
        """

        all_paragraphs = self._execute_cypher(query, module_id=module_id)

        # Count paragraphs in the range
        in_range = False
        count = 0

        for paragraph in all_paragraphs:
            if paragraph["paragraph_name"] == start_name:
                in_range = True

            if in_range:
                count += 1

            if paragraph["paragraph_name"] == end_name:
                in_range = False
                break

        return max(count, 2)  # At least start and end
