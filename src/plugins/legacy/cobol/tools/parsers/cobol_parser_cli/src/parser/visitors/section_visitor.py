from typing import Dict, Any, Optional

from .base_visitor import BaseVisitor

class SectionVisitor(BaseVisitor):
    """Visitor for processing COBOL sections"""
    
    def visit_cobol_section(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Visit a CobolSection node
        
        Args:
            node: The CobolSection node
            
        Returns:
            The processed node
        """
        self.logger.debug(f"Visiting Section: {node.get('section_name')}")
        
        # Update paragraphs with parent name
        for child in node.get("children", []):
            if child.get("type") in ["CobolParagraph", "CobolEntryParagraph"]:
                child["parent_name"] = node.get("section_name", "")
        
        return self.generic_visit(node)
