import os
import yaml
import logging
from typing import Dict, Any

class Config:
    """Configuration handler for COBOL Parser"""
    
    def __init__(self, config_file: str = None):
        """Initialize configuration from file or default values"""
        self.config_data: Dict[str, Any] = {}
        
        # Load default configuration first
        default_config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                        'config', 'default.yaml')
        
        if os.path.exists(default_config_path):
            with open(default_config_path, 'r') as f:
                self.config_data = yaml.safe_load(f)
        
        # Override with user-provided configuration
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                user_config = yaml.safe_load(f)
                self._merge_config(self.config_data, user_config)
    
    def _merge_config(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> None:
        """Recursively merge configuration dictionaries"""
        for key, value in override_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def get(self, key_path: str, default=None) -> Any:
        """Get a configuration value using dot notation
        
        Args:
            key_path: Path to the configuration value (e.g. 'neo4j.url')
            default: Default value if the key is not found
            
        Returns:
            The configuration value or default
        """
        keys = key_path.split('.')
        current = self.config_data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return current
    
    def setup_logging(self) -> None:
        """Configure logging based on configuration"""
        log_level_name = self.get('logging.level', 'INFO')
        log_level = getattr(logging, log_level_name)
        log_file = self.get('logging.file', 'cobol_parser.log')
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
