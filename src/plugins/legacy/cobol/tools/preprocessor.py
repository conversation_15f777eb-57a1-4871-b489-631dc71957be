import os
import re
import logging
import tempfile
import yaml
from dotenv import load_dotenv

from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.orchestrator import Orchestrator
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.config import Config
from src.plugins.legacy.cobol.tools.parsers.cobol_parser_cli.src.call_tree.orchestrator import CallTreeOrchestrator

# Load environment variables
load_dotenv()

logger = logging.getLogger("tools.preprocessors.cobol")


# noinspection PyMethodMayBeStatic
class CobolPreprocessor:
    """
    Preprocessor for COBOL files that uses the standalone COBOL parser CLI.

    This preprocessor integrates with the advanced COBOL parser for:
    1. COPY statement expansion
    2. Comment handling
    3. Line continuation handling
    4. COBOL syntax parsing
    """

    def __init__(self):
        """Initialize the COBOL preprocessor"""
        pass

    def preprocess_file(self, input_path: str, output_path: str) -> bool:
        """
        Preprocess a COBOL file using the standalone COBOL parser CLI

        Args:
            input_path: Path to the input COBOL file
            output_path: Path where the preprocessed file will be written

        Returns:
            bool: True if preprocessing was successful, False otherwise
        """
        logger.info(f"Preprocessing COBOL file: {input_path}")

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Find copybook directory - use the first one found in this priority order
        possible_dirs = [
            os.path.join(os.path.dirname(input_path), "copybooks"),
            os.path.join(os.path.dirname(input_path), "../copybooks/cobol"),
            os.path.dirname(input_path)
        ]

        # Convert to absolute paths
        possible_dirs = [os.path.abspath(d) for d in possible_dirs]

        # Log all the places we're looking
        logger.info(f"Searching for copybooks in the following directories:")
        for dir_path in possible_dirs:
            logger.info(f"- {dir_path} (exists: {os.path.exists(dir_path)})")

        # Select the first directory that exists
        copybook_dir = None
        for dir_path in possible_dirs:
            if os.path.exists(dir_path):
                copybook_dir = dir_path
                logger.info(f"Found copybook directory: {copybook_dir}")
                break

        if not copybook_dir:
            logger.error(f"No copybook directory found")
            return False

        logger.info(f"Using copybook directory: {copybook_dir}")

        # Get program name
        program_name_upper = os.path.splitext(os.path.basename(input_path))[0].upper()
        program_name_orig = os.path.splitext(os.path.basename(input_path))[0]

        # Create a temporary directory for the parser output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a temporary config file
            config_file_path = os.path.join(temp_dir, "temp_cobol_config.yaml")

            # Create configuration for the Orchestrator
            config_data = {
                "directories": {
                    "copybooks": copybook_dir,  # Single directory instead of list
                    "output": temp_dir
                },
                "parser": {
                    "preserve_comments": True,
                    "max_recursion_depth": 100,
                    "output_preprocessed": True,
                    "expand_copybooks": True,
                    "debug_copybooks": True
                },
                "logging": {
                    "level": "DEBUG",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                },
                # Get Neo4j details from environment variables with fallbacks
                "neo4j": {
                    "enabled": True,
                    "url": os.getenv("NEO4J_URL", ""),
                    "user": os.getenv("NEO4J_USER", ""),
                    "password": os.getenv("NEO4J_PASSWORD", ""),
                    "batch_size": int(os.getenv("NEO4J_BATCH_SIZE", "100"))
                }
            }

            # Write the config to the temporary file
            try:
                with open(config_file_path, 'w') as f:
                    yaml.dump(config_data, f)
            except (PermissionError, OSError) as e:
                logger.error(f"Failed to write config file {config_file_path}: {str(e)}")
                return False

            # Create a configuration object with the file path
            config = Config(config_file=config_file_path)

            # Override Neo4j settings programmatically to ensure it's disabled
            # TO-DO: make sure we have a configuration to switch neo4j on or off
            # config.config_data["neo4j"]["enabled"] = False
            # config.config_data["neo4j"]["url"] = ""

            # Create the Orchestrator with explicitly disabled Neo4j
            try:
                orchestrator = Orchestrator(config)
                orchestrator.graph_db_connector.connection = None
            except Exception as e:
                logger.error(f"Failed to initialize orchestrator: {str(e)}")
                return False

            # Since we now explicitly save preprocessed files in the orchestrator,
            # we can simply process the file, then look for the expected outputs
            logger.info(f"Processing file with COBOL parser CLI: {input_path}")

            # Process the source code directly using the orchestrator components
            # This allows us to ensure the preprocessed files are created as expected
            try:
                with open(input_path, 'r', encoding='utf-8', errors='replace') as f:
                    source_text = f.read()

                # Explicitly run the preprocessing steps
                logger.info(f"Expanding copybooks for {input_path}")
                expanded_text = orchestrator.copybook_expander.expand_source(source_text, os.path.basename(input_path))
                logger.info(f"Normalizing text for {input_path}")
                ## TODO ?? Why we do this here and here
                ##  src/plugins/legacy/cobol/tools/parsers/cobol_parser_cli/src/orchestrator.py:119
                normalized_text = orchestrator.text_normalizer.normalize(expanded_text) # TODO ?? why we do this here and here src/plugins/legacy/cobol/tools/parsers/cobol_parser_cli/src/orchestrator.py:119

                # Save preprocessed file directly
                preprocessed_file = os.path.join(temp_dir, f"{program_name_upper}_preprocessed.cob")
                with open(preprocessed_file, 'w', encoding='utf-8') as f:
                    f.write(normalized_text)

                # Also run the full process to get the IR
                success, failures = orchestrator.process_files([input_path])
                if not success:
                    logger.error(f"Failed to generate IR file: {failures}")
                    # Try to create a minimal IR file as fallback
                    ir_file_path = os.path.join(temp_dir, f"{program_name_upper}.json")
                    self._create_fallback_ir_file(ir_file_path, program_name_upper, normalized_text)
                else:
                    # Expected IR file path
                    ir_file_path = os.path.join(temp_dir, f"{program_name_upper}.json")

                # List all files in the temp directory for debugging
                logger.debug(f"Files in output directory {temp_dir}:")
                for file in os.listdir(temp_dir):
                    logger.debug(f"- {file}")

                # Check if our expected files were created
                if not os.path.exists(preprocessed_file):
                    logger.error(f"Preprocessed file not found: {preprocessed_file}")
                    return False

                if not os.path.exists(ir_file_path):
                    logger.error(f"IR file not found: {ir_file_path}")
                    # Create fallback IR file
                    self._create_fallback_ir_file(ir_file_path, program_name_upper, normalized_text)
                    if not os.path.exists(ir_file_path):
                        logger.error(f"Failed to create fallback IR file: {ir_file_path}")
                        return False

                logger.info(f"Found preprocessed file: {preprocessed_file}")
                logger.info(f"Found IR file: {ir_file_path}")

            except Exception as e:
                logger.error(f"Error preprocessing file: {str(e)}")
                return False

            # Save the preprocessed file and IR file
            with open(preprocessed_file, 'r', encoding='utf-8', errors='replace') as src_file:
                preprocessed_content = src_file.read()
                with open(output_path, 'w', encoding='utf-8') as dest_file:
                    dest_file.write(preprocessed_content)

                # Check for COPY statements that might not have been expanded (for logging only)
                copy_statements = re.findall(r'^\s*COPY\s+', preprocessed_content, re.IGNORECASE)
                if copy_statements:
                    logger.warning(f"Found {len(copy_statements)} COPY statements in preprocessed file. Some copybooks might not have been found.")

            # Create JSON directory structure for IR files
            json_dir = os.path.join(os.path.dirname(output_path), "json")
            os.makedirs(json_dir, exist_ok=True)

            # Save IR file in the json subdirectory
            ir_output_filename = f"{os.path.basename(os.path.splitext(output_path)[0])}.json"
            ir_output_path = os.path.join(json_dir, ir_output_filename)

            with open(ir_file_path, 'r', encoding='utf-8', errors='replace') as src_file:
                with open(ir_output_path, 'w', encoding='utf-8') as dest_file:
                    dest_file.write(src_file.read())

            logger.info(f"Successfully preprocessed COBOL file: {output_path}")
            logger.info(f"Successfully saved IR file to: {ir_output_path}")

            # Run call tree analysis
            call_tree_success = self._generate_call_tree_analysis(
                config=config,
                program_name=program_name_upper
            )

            if call_tree_success:
                logger.info(f"Successfully executed call tree analysis for {output_path}")
            else:
                logger.warning(f"Failed to execute call tree analysis for {output_path}")

            # Run REKT analysis
            # flow_success = self._generate_flow_diagram(
            #     input_file_path=input_path,
            #     preprocessed_file_path=output_path,
            #     output_dir=os.path.dirname(output_path),
            #     copybook_dirs=[copybook_dir]
            # )
            #
            # if flow_success:
            #     logger.info(f"Successfully executed REKT analysis for {output_path}")
            # else:
            #     logger.warning(f"Failed to execute REKT analysis for {output_path}")

            return True

    @property
    def version(self) -> str:
        """Get the version of the COBOL preprocessor"""
        return "3.0.0"

    # def _create_temp_copybooks(self, temp_dir: str) -> str:
    #     """
    #     Create common mainframe copybooks in a temporary directory.
    #
    #     Args:
    #         temp_dir: Temporary directory where copybooks will be created
    #
    #     Returns:
    #         str: Path to the created copybooks directory
    #     """
    #     logger.info("Creating temporary mainframe copybooks")
    #     copybooks_dir = os.path.join(temp_dir, "mainframe_copybooks")
    #     os.makedirs(copybooks_dir, exist_ok=True)
    #
    #     # Common mainframe copybooks
    #     copybooks = {
    #         "DFHAID": """
    #    01  DFHAID.
    #        05  DFHNULL         PIC X VALUE IS X'00'.
    #        05  DFHENTER        PIC X VALUE IS X'7D'.
    #        05  DFHCLEAR        PIC X VALUE IS X'6D'.
    #        05  DFHPEN          PIC X VALUE IS X'6B'.
    #        05  DFHOPID         PIC X VALUE IS X'F1'.
    #        05  DFHMSRE         PIC X VALUE IS X'F2'.
    #        05  DFHSTRF         PIC X VALUE IS X'F3'.
    #        05  DFHTRIG         PIC X VALUE IS X'F4'.
    #        05  DFHPA1          PIC X VALUE IS X'6C'.
    #        05  DFHPA2          PIC X VALUE IS X'6E'.
    #        05  DFHPA3          PIC X VALUE IS X'6B'.
    #        05  DFHPF1          PIC X VALUE IS X'F1'.
    #        05  DFHPF2          PIC X VALUE IS X'F2'.
    #        05  DFHPF3          PIC X VALUE IS X'F3'.
    #        05  DFHPF4          PIC X VALUE IS X'F4'.
    #        05  DFHPF5          PIC X VALUE IS X'F5'.
    #        05  DFHPF6          PIC X VALUE IS X'F6'.
    #        05  DFHPF7          PIC X VALUE IS X'F7'.
    #        05  DFHPF8          PIC X VALUE IS X'F8'.
    #        05  DFHPF9          PIC X VALUE IS X'F9'.
    #        05  DFHPF10         PIC X VALUE IS X'7A'.
    #        05  DFHPF11         PIC X VALUE IS X'7B'.
    #        05  DFHPF12         PIC X VALUE IS X'7C'.
    #        05  DFHPF13         PIC X VALUE IS X'C1'.
    #        05  DFHPF14         PIC X VALUE IS X'C2'.
    #        05  DFHPF15         PIC X VALUE IS X'C3'.
    #        05  DFHPF16         PIC X VALUE IS X'C4'.
    #        05  DFHPF17         PIC X VALUE IS X'C5'.
    #        05  DFHPF18         PIC X VALUE IS X'C6'.
    #        05  DFHPF19         PIC X VALUE IS X'C7'.
    #        05  DFHPF20         PIC X VALUE IS X'C8'.
    #        05  DFHPF21         PIC X VALUE IS X'C9'.
    #        05  DFHPF22         PIC X VALUE IS X'4A'.
    #        05  DFHPF23         PIC X VALUE IS X'4B'.
    #        05  DFHPF24         PIC X VALUE IS X'4C'.
    #         """,
    #
    #         "DFHBMSCA": """
    #    01  DFHBMSCA.
    #        05  DFHBMPEM        PIC X VALUE IS X'19'.
    #        05  DFHBMPNL        PIC X VALUE IS X'15'.
    #        05  DFHBMUNP        PIC X VALUE IS X'14'.
    #        05  DFHBMUNN        PIC X VALUE IS X'16'.
    #        05  DFHBMPRO        PIC X VALUE IS X'11'.
    #        05  DFHBMBRY        PIC X VALUE IS X'1D'.
    #        05  DFHBMDAR        PIC X VALUE IS X'0C'.
    #        05  DFHBMFSE        PIC X VALUE IS X'1E'.
    #        05  DFHBMPRF        PIC X VALUE IS X'18'.
    #        05  DFHBMASF        PIC X VALUE IS X'10'.
    #        05  DFHBMASB        PIC X VALUE IS X'10'.
    #        05  DFHBMEOF        PIC X VALUE IS X'80'.
    #        05  DFHBMDET        PIC X VALUE IS X'0A'.
    #        05  DFHBMASK        PIC X VALUE IS X'0D'.
    #         """,
    #
    #         "DFHEIVAR": """
    #    01  DFHEIVAR.
    #        05  EIBTRNID        PIC X(4).
    #        05  EIBTRMID        PIC X(4).
    #        05  EIBTASKN        PIC S9(7) COMP-3.
    #        05  EIBTRMCD        PIC X(2).
    #        05  EIBCPOSN        PIC S9(4) COMP.
    #        05  EIBCALEN        PIC S9(4) COMP.
    #        05  EIBAID          PIC X.
    #        05  EIBFN           PIC X(2).
    #        05  EIBRCODE        PIC X(6).
    #        05  EIBDS           PIC X(8).
    #        05  EIBRSRCE        PIC X(8).
    #        05  EIBSYNC         PIC X.
    #        05  EIBFREE         PIC X.
    #        05  EIBRECV         PIC X.
    #        05  EIBFIL01        PIC X.
    #        05  EIBATT          PIC X.
    #        05  EIBEOC          PIC X.
    #        05  EIBFMH          PIC X.
    #        05  EIBCOMPL        PIC X.
    #        05  EIBSIG          PIC X.
    #        05  EIBCONF         PIC X.
    #        05  EIBERR          PIC X.
    #        05  EIBERRCD        PIC X(4).
    #        05  EIBSYNRB        PIC X.
    #        05  EIBNODAT        PIC X.
    #        05  EIBRESP         PIC S9(8) COMP.
    #        05  EIBRESP2        PIC S9(8) COMP.
    #        05  EIBRLDBK        PIC X.
    #         """,
    #
    #         "DFHCOMMAREA": """
    #    01  DFHCOMMAREA        PIC X(1).
    #         """,
    #
    #         "SQL": """
    #        EXEC SQL BEGIN DECLARE SECTION END-EXEC.
    #        EXEC SQL END DECLARE SECTION END-EXEC.
    #         """
    #     }
    #
    #     # Create each copybook file
    #     for name, content in copybooks.items():
    #         filepath = os.path.join(copybooks_dir, f"{name}.cpy")
    #         with open(filepath, "w", encoding="utf-8") as f:
    #             f.write(content)
    #         logger.debug(f"Created copybook: {filepath}")
    #
    #     logger.info(f"Created temporary copybooks in directory: {copybooks_dir}")
    #     return copybooks_dir

    def _generate_call_tree_analysis(self, config: Config, program_name: str) -> bool:
        """
        Generate call tree analysis for a COBOL program using the cobol_parser_cli tool.

        Args:
            config: The config object for the parser
            program_name: The name of the program to analyze

        Returns:
            bool: True if call tree analysis was successful, False otherwise
        """
        try:
            # Create a CallTreeOrchestrator with the same config
            logger.info(f"Generating call tree analysis for {program_name}")
            call_tree_orchestrator = CallTreeOrchestrator(config)

            # Run the analysis
            analysis_types = ["calls", "goto", "perform", "cics"]
            results = call_tree_orchestrator.analyze(analysis_types)

            if "error" in results:
                logger.error(f"Failed to generate call tree analysis: {results.get('error')}")
                return False

            # Log the results
            nodes_created = results.get('nodes_created', 0)
            relationships_created = results.get('relationships_created', 0)
            logger.info(f"Call tree analysis created {nodes_created} nodes and {relationships_created} relationships")

            return True

        except Exception as e:
            logger.error(f"Error generating call tree analysis: {str(e)}")
            return False

    # def _generate_flow_diagram(self, input_file_path: str, preprocessed_file_path: str, output_dir: str, copybook_dirs: List[str] = None) -> bool:
    #     """
    #     Generate flow diagram for a COBOL file using the cobol_rekt tool.
    #
    #     Args:
    #         input_file_path: Path to the original COBOL file
    #         preprocessed_file_path: Path to the preprocessed COBOL file (used to determine output location)
    #         output_dir: Directory where the flow diagram will be saved
    #         copybook_dirs: List of directories containing copybooks
    #
    #     Returns:
    #         bool: True if flow diagram generation was successful, False otherwise
    #     """
    #     from src.plugins.legacy.cobol.tools.parsers.cobol_rekt.smojol_wrapper import SmojolCli
    #
    #     try:
    #         # Create directory for flow diagrams if it doesn't exist
    #         rekt_dir = os.path.join(output_dir, "rekt")
    #         os.makedirs(rekt_dir, exist_ok=True)
    #
    #         # Get program name from the preprocessed file path
    #         program_name = os.path.splitext(os.path.basename(preprocessed_file_path))[0]
    #
    #         # Use original COBOL file directory and input file directly
    #         source_dir = os.path.dirname(input_file_path)
    #         file_name = os.path.basename(input_file_path)
    #
    #         # Create temp directory for the mainframe copybooks
    #         with tempfile.TemporaryDirectory() as temp_dir:
    #             # Create temp copybooks directory with common mainframe copybooks
    #             temp_copybooks_dir = self._create_temp_copybooks(temp_dir)
    #
    #             # Combine user-provided copybook directories with temp copybooks
    #             all_copybook_dirs = [temp_copybooks_dir]
    #             if copybook_dirs:
    #                 all_copybook_dirs.extend(copybook_dirs)
    #
    #             # Set up output directory for the flow diagram report
    #             report_dir = os.path.join(temp_dir, "report")
    #
    #             # Initialize SmojolCli
    #             logger.info(f"Generating flow diagram for {program_name} using {input_file_path}")
    #             smojol = SmojolCli(
    #                 source_dir=source_dir,
    #                 copybook_dirs=all_copybook_dirs,
    #                 report_dir=report_dir,
    #                 commands=["BUILD_BASE_ANALYSIS", "DRAW_FLOWCHART", "WRITE_DATA_STRUCTURES", "WRITE_CFG" ],
    #                 dialect="COBOL",  # Use standard COBOL dialect
    #                 flowchart_output_format="PNG",
    #                 flowchart_generation="PROGRAM",
    #                 permissive_search=True
    #             )
    #
    #             # Run the tool
    #             result = smojol.run([file_name])
    #
    #             if not result["success"]:
    #                 logger.error(f"Failed to generate flow diagram: {result.get('error', 'Unknown error')}")
    #                 if result.get("stderr"):
    #                     logger.error(f"Error details: {result['stderr']}")
    #                 return False
    #
    #             # Find the generated diagram
    #             flow_files = []
    #             for root, dirs, files in os.walk(report_dir):
    #                 for file in files:
    #                     flow_files.append(os.path.join(root, file))
    #
    #             if not flow_files:
    #                 logger.warning(f"No flow diagram found for {program_name}")
    #                 # List all files in report directory for debugging
    #                 logger.debug(f"Files in report directory {report_dir}:")
    #                 for root, dirs, files in os.walk(report_dir):
    #                     for file in files:
    #                         logger.debug(f"- {os.path.join(root, file)}")
    #                 return False
    #
    #             # Copy the results to the output directory
    #             for file in flow_files:
    #                 p = Path(file)
    #                 os.makedirs(os.path.join(rekt_dir, p.parts[-2]), exist_ok=True)
    #                 shutil.copyfile(file, os.path.join(rekt_dir,*p.parts[-2:]))
    #
    #             logger.info(f"Successfully executed REKT analysis: {rekt_dir}")
    #             return True
    #
    #     except Exception as e:
    #         logger.error(f"Error generating flow diagram: {str(e)}")
    #         return False

    def _create_fallback_ir_file(self, ir_file_path: str, program_name: str, source_text: str) -> None:
        """
        Create a minimal IR file when the full parsing fails.
        This allows the chunker to still process the file with basic structure.

        Args:
            ir_file_path: Path where the IR file should be created
            program_name: Name of the COBOL program
            source_text: The preprocessed source text
        """
        logger.info(f"Creating fallback IR file for {program_name}")

        try:
            # Create a minimal IR structure with basic divisions
            fallback_ir = {
                "metadata": {
                    "module_id": program_name,
                    "file_name": f"{program_name}.cbl",
                    "fallback": True
                },
                "nodes": [
                    {
                        "uuid": f"{program_name}_module",
                        "type": "CobolModule",
                        "module_id": program_name,
                        "file_name": f"{program_name}.cbl",
                        "full_text": source_text
                    }
                ],
                "relationships": []
            }

            # Try to identify basic divisions in the source text
            lines = source_text.split('\n')
            current_division = None
            division_content = []

            for line in lines:
                line_upper = line.strip().upper()

                if 'IDENTIFICATION DIVISION' in line_upper:
                    if current_division:
                        self._add_division_to_ir(fallback_ir, current_division, '\n'.join(division_content), program_name)
                    current_division = 'IDENTIFICATION'
                    division_content = [line]
                elif 'ENVIRONMENT DIVISION' in line_upper:
                    if current_division:
                        self._add_division_to_ir(fallback_ir, current_division, '\n'.join(division_content), program_name)
                    current_division = 'ENVIRONMENT'
                    division_content = [line]
                elif 'DATA DIVISION' in line_upper:
                    if current_division:
                        self._add_division_to_ir(fallback_ir, current_division, '\n'.join(division_content), program_name)
                    current_division = 'DATA'
                    division_content = [line]
                elif 'PROCEDURE DIVISION' in line_upper:
                    if current_division:
                        self._add_division_to_ir(fallback_ir, current_division, '\n'.join(division_content), program_name)
                    current_division = 'PROCEDURE'
                    division_content = [line]
                else:
                    if current_division:
                        division_content.append(line)

            # Add the last division
            if current_division:
                self._add_division_to_ir(fallback_ir, current_division, '\n'.join(division_content), program_name)

            # Write the fallback IR file
            import json
            try:
                with open(ir_file_path, 'w', encoding='utf-8') as f:
                    json.dump(fallback_ir, f, indent=2)
                logger.info(f"Successfully created fallback IR file: {ir_file_path}")
            except Exception as json_error:
                # Clean up the file if JSON writing failed
                if os.path.exists(ir_file_path):
                    # noinspection PyBroadException
                    try:
                        os.remove(ir_file_path)
                    except:
                        pass  # Ignore cleanup errors
                raise json_error

        except Exception as e:
            logger.error(f"Error creating fallback IR file: {str(e)}")

    def _add_division_to_ir(self, ir_data: dict, division_type: str, content: str, program_name: str) -> None:
        """
        Add a division node to the IR data structure.

        Args:
            ir_data: The IR data structure to modify
            division_type: Type of division (IDENTIFICATION, ENVIRONMENT, DATA, PROCEDURE)
            content: The division content
            program_name: Name of the program
        """
        division_node = {
            "uuid": f"{program_name}_{division_type}_DIV",
            "type": f"Cobol{division_type.title()}Division",
            "module_id": program_name,
            "full_text": content
        }

        if division_type == 'IDENTIFICATION':
            # Try to extract program ID
            for line in content.split('\n'):
                if 'PROGRAM-ID' in line.upper():
                    division_node["program_id"] = program_name
                    break

        ir_data["nodes"].append(division_node)

        # Add relationship to module
        relationship = {
            "uuid": f"{program_name}_{division_type}_REL",
            "type": "INCLUDES",
            "from_uuid": f"{program_name}_module",
            "to_uuid": f"{program_name}_{division_type}_DIV"
        }
        ir_data["relationships"].append(relationship)

    @property
    def encoding(self) -> str:
        """Get the default encoding for COBOL files"""
        return "utf-8"