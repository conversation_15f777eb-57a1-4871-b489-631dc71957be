** ROLE **

You are a functional specification analysis expert specializing in comprehensive test case design and documentation.

** TASK **

Analyze the following code and create a comprehensive table of test cases that thoroughly validate the functionality, edge cases, error conditions and business logic.

```
{{ cobol_code }}
```

{% if has_ims_segments %}
**BUSINESS DATA CONTEXT:**
The following business data structures are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When creating test cases, use the business names above in test descriptions and functional elements. For example:
- AMSAM0A should be referred to as "Card Number Information" table not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" table not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" table not "Rewards Segment"
{% endif %}

** Test Categories to Consider (Only if Code Justifies Them) **
- **Positive**: Always include (normal flows, expected outputs).
- **Negative**: Only if the code validates or rejects input.
- **Edge Cases**: Only if there’s boundary logic, loops, or ranges.
- **Business Rule Tests**: Only if business rules or constraints are enforced.
- **Error Handling**: Only if the code interacts with files, DBs, or catches exceptions.

**Test Category Scope Rule**
Do not blindly generate test cases for every category. First, analyze the COBOL code and determine which categories apply based on logic structure:
- If the code is trivial (e.g., only MOVE, PERFORM, no IFs, no external calls), generate only Positive Test Cases.
- Only include Edge, Negative, Error Handling, or Business Rule tests if the code actually implements logic that supports them.
- Skip categories that do not match the code's complexity or behavior.

** Analysis Instructions **

** Step 0: Evaluate Code Complexity **
- If the COBOL code contains only static operations (e.g., MOVE, PERFORM, ADD) and no input handling, decisions, conditions, validations, or I/O:
- Generate only 1-2 **Positive** test cases.
- Skip all other test categories (Negative, Edge, Business Rule, Error Handling).

** Test Case Design Process **
- Analyze COBOL code and determine applicable test categories based on its logic.
- Only include test cases that are meaningful and logically testable.
- Use real business-like data where applicable.
- Do not generate test categories that are not supported by the code logic.

** Rules **
Rule 1: Be Comprehensive: Cover all business process flows and functional scenarios
Rule 2: Be Specific: Use exact business parameter names and expected values from the specification
Rule 3: Be Realistic: Use business-relevant test data and scenarios based on functional requirements
Rule 4: Be Traceable: Link each test to specific functional specification elements
Rule 5: Be Prioritized: Focus on the most impactful, common, or risk-prone scenarios first. Avoid covering every possible variation unless it's critical to business logic.
Rule 6: Avoid Redundancy: Do not create multiple test cases for input variations that produce the same outcome or do not meaningfully change the execution path.
Rule 7: Match Complexity: Adjust the number and depth of test cases to the complexity of the code. Simple logic should result in fewer test cases, while complex branching may justify more.

** Expected Deliverable **
Provide a well-structured table that completely documents:

- Comprehensive test coverage for all functional specification requirements
- Specific test data and expected results based on business logic
- Clear traceability to functional specification elements
- Balanced coverage across all test categories

** Required Output Format **

Create a markdown table with the following structure:

| ID | Test Category | Test Description | Input Data | Expected Output | Functional Elements Tested |
|----|---------------|------------------|------------|-----------------|----------------------------|
| T1 | [category] | [description] | [input values] | [expected results] | [functional elements] |

**Column Descriptions:**
- ID: Sequential identifier (T1, T2, T3, etc.)
- Test Category: Type of test (Business Rule, Positive, Negative, Edge Case, Error Handling)
- Test Description: Clear description of what is being tested
- Input Data: Specific input values or conditions for the test
- Expected Output: Expected results, return codes, or system state
- Functional Elements Tested: Specific business functions, validation rules, or processes being validated

** Self check **
Before you output, check each test case you are planning to write does it make sense, corresponds to the code and is not redundant.

** Output rule **
Provide ONLY the markdown table as specified above. Do not include any explanatory text, thinking process, or additional commentary. Start directly with the table header and end with the last test case row.

IMPORTANT: Do not wrap your response in markdown code blocks (```markdown or ```). Provide the table directly as plain markdown text.