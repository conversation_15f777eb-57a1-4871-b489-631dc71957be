You are an expert COBOL code analyst and software architect.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

{% if has_ims_segments %}
**BUSINESS DATA CONTEXT:**
The following business data structures are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When describing the business purpose, use the business names above instead of generic terms. For example:
- AMSAM0A should be referred to as "Card Number Information" table not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" table not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" table not "Rewards Segment"
{% endif %}

GENERATE THE FUNCTION business PURPOSE SECTION of a functional specification.

Provide a brief statement of what this function does from a business perspective (1 paragraph).

IMPORTANT GUIDELINES:
1. Focus on the business purpose and what the function accomplishes.
2. Be concise but comprehensive - this should be exactly 1 paragraph.

FORMAT YOUR RESPONSE AS:
## FUNCTION PURPOSE
[Your 1-paragraph description here]

Do not add any additional commentary or explanations, just the function purpose section.
