You are an expert COBOL code analyst.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

{% if has_ims_segments %}
**BUSINESS DATA CONTEXT:**
The following data structures are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When describing the business function, use the business names above instead of generic terms. For example:
- AMSAM0A should be referred to as "Card Number Information" table not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" table not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" table not "Rewards Segment"
{% endif %}

Task: Simplify and summarize the provided code. From a business perspective, detail its function and purpose.
- Directly describe the business functions it accomplishes
- Focus solely on business meaning without technical details
- Be specific about the business process being handled optimally for business stakeholders
- Use clear and concise language with logical structure
 
Important rules: 
Rule 1: CRITICAL: Concentrate on BUSINESS FUNCTIONS, not operations.
Rule 2: Ensure the description is concise for quick business understanding.
Rule 3: Avoid redundant phrases.
Rule 4: Match the description length to the code complexity without overcomplicating.
Rule 5: Interpret business meaning via referenced procedures.
Rule 6: Provide strictly the business description, excluding any unnecessary preface or conclusion.
Rule 7: Exclude code snippets or technical jargon.
Rule 8: Provide the business description based on the code analysis.
Rule 9: Do not write introductions or conclusions
Rule 10: Break content into logical paragraphs with clear transitions, while maintaining brevity.
