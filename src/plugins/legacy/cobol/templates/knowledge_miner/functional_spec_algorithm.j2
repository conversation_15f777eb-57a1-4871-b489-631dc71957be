You are an expert COBOL code analyst and software architect.
Analyze the following COBOL code chunk from program {{ program_id }}, chunk {{ chunk_name }}:

```cobol
{{ code_content }}
```

{{ context }}

{% if has_ims_segments %}
**BUSINESS DATA CONTEXT:**
The following business data structures are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When describing database operations, use the business names above instead of generic terms like "Account Sub-Segment". For example:
- AMSAM0A should be referred to as "Card Number Information" table not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" table not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" table not "Rewards Segment"
{% endif %}

GENERATE THE ALGORITHM SECTION of a functional specification.

Provide a step-by-step detailed description of the logic flow (human-readable, not pseudocode). Do not skip any step. Focus on the business logic, not technical implementation. Focus on the Input parameters permutations leading to Output parameters. All constants (like display messages) should be defined in the specification. Pay attention to input and output parameters for external calls.

**CRITICAL: USE BUSINESS-FOCUSED LANGUAGE ONLY**

**Database Operation Replacements - MANDATORY:**
- Replace technical operations with business descriptions:
  * "Retrieve record from [Business Data Name]" table (not "EXEC DLI GU")
  * "Create new record in [Business Data Name]" table (not "EXEC DLI ISRT")
  * "Update existing record in [Business Data Name]" table (not "EXEC DLI REPL")
  * "Remove record from [Business Data Name]" table (not "EXEC DLI DLET")
  * "Query data from [Business Data Name]" table (not "EXEC SQL SELECT")
  * "Add new record to [Business Data Name]" table (not "EXEC SQL INSERT")

**Status Validation Replacements - MANDATORY:**
- Replace technical status checks with business validation:
  * "Validate operation completion status" (not "Check DIBSTAT")
  * "If operation completed successfully" (not "IF DIBSTAT = SPACES")
  * "If no matching record was found" (not "IF DIBSTAT = 'GE'")
  * "If end of data was reached" (not "IF DIBSTAT = 'GB'")
  * "Validate transaction result" (not "Check SQLCODE")
  * "If transaction completed successfully" (not "IF SQLCODE = 0")

**IMPORTANT GUIDELINES:**
1. Replace COBOL identifiers with business names, but keep original name in brackets, e.g., "Customer Identifier (COBOL: CUSTOMER-ID)".
2. Be very specific and detailed. "Update status indicators based on the procedure's output" is not acceptable, specify the actual values must be set on which condition.
3. IMPORTANT: When invoking external functions (ex-COBOL Paragraph or external COBOL Program), specify the exact parameters to be passed and indicate save locations for the results. Do not forget to return the results of the function. Specify the exact return values. When it is needed to invoke external function - don't say "Call the procedure for <>", specify the business action we need to perform (like "Read the next Account Record from the file"). But add in the brackets add function name (business function name and COBOL Identifier) and the exact parameters to be passed.
Example:
    Step X: Retrieve Next Account Record
        Call Account Record Retrieval function (COBOL: 1000-ACCTFILE-GET-NEXT) with parameters:
            Input: Account File (COBOL: ACCTFILE-FILE) - VSAM KSDS dataset containing account records
            Input: Account File Status (COBOL: ACCTFILE-STATUS) - current status of VSAM operations
            Input: Application End of File indicator (COBOL: APPL-EOF) - current end-of-file status
        Receive returned values:
            Updated Account Record (COBOL: ACCOUNT-RECORD) - retrieved account record structure
            Updated Application Result (COBOL: APPL-RESULT) - operation result code (0=success, 12=error, 16=end-of-file)
            Updated End Of File Indicator (COBOL: END-OF-FILE) - set to 'Y' if end of file reached
            Updated IO Status (COBOL: IO-STATUS) - contains account file status when error occurs
4. This algorithm will be used to generate Java code then, so don't skip any details.
5. File operations should be described as "Read the next Account Record (COBOL: ACCOUNT-RECORD) from the Account File (COBOL: ACCTFILE-FILE)"
6. If you see database operations — describe the operation using the business names from the Business Data Context above. For example:
   - For data retrieval operations on AMSAM0A say "Retrieve record from Card Number Information (COBOL: AMSAM0A)"
   - For data creation operations on CUSCM01 say "Create new record in Customer Address Information (COBOL: CUSCM01)"
   - For data update operations on RWSRW00 say "Update existing record in Rewards Information (COBOL: RWSRW00)"
   - For data removal operations say "Remove record from [Business Data Name] (COBOL: [SEGMENT])"
   - For data queries say "Query data from [Business Data Name] (COBOL: [TABLE])"
   - NEVER use technical operation codes or generic terms - always use the specific business names provided above.
7. File operations should be described as "Process data from [File Name]" or "Write data to [File Name]".

FORMAT YOUR RESPONSE AS:
## ALGORITHM
[Your step-by-step algorithm description here]

Do not add any additional commentary or explanations, just the algorithm section.
