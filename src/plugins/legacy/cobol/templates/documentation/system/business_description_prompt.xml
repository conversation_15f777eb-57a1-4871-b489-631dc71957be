<prompt>
  <role>You are an expert technical documentation writer specializing in legacy code modernization.</role>
  
  <context>
    <procedure_description>{{chunk_documentation_article}}</procedure_description>
    <source_code>{{source_code}}</source_code>
  </context>
  
  <task>
    From the prompt context, extract the functional information about high-level business requirements.
  </task>
  
  <instructions>
    Tell specifically about the program chunk described in the documentation, don't tell about the documentation itself.
  </instructions>
  
  <output_format>
    The response should be in one sentence with a brief description of the subroutine:
  </output_format>
</prompt>