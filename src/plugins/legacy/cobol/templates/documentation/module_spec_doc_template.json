{"doc_root": {"document_divisions": [{"level_2": {"caption": "Functional Specification", "content": "{{recap_table}}"}}, {"level_2": {"caption": "Table of Contents", "content": "{{document_contents}}"}}, {"level_2": {"caption": "Functional information", "document_divisions": [{"level_3": {"caption": "High-Level Business Requirements", "content": "{{high_level_business_requirements}}"}}, {"level_3": {"caption": "Process Flow step sequence", "content": "{{process_flow_step_sequence_table}}"}}, {"level_3": {"caption": "Business Logic", "content": "{{business_logic}}"}}]}}, {"level_2": {"caption": "Main Program Process Flow Diagram", "content": "{{process_flow_diagram}}", "level_3": {"caption": "Diagram Overview", "content": "{{rekt_diagram_overview}}"}}}, {"level_2": {"caption": "Process description", "document_divisions": [{"level_3": {"caption": "Preconditions", "document_divisions": [{"level_4": {"caption": "Incoming files", "content": "{{incoming_files_table}}"}}, {"level_4": {"caption": "Input parameters", "content": "{{input_parameters_table}}"}}]}}, {"level_3": {"caption": "Database interactions", "document_divisions": [{"level_4": {"caption": "DB2 Tables used", "content": "{{include_db2_tables_table}}"}}, {"level_4": {"caption": "DB2 Tables select / read", "content": "{{select_db2_tables_table}}"}}, {"level_4": {"caption": "DB2 Tables insert", "content": "{{insert_db2_tables_table}}"}}, {"level_4": {"caption": "IMS Tables used", "content": "{{include_ims_tables_table}}"}}, {"level_4": {"caption": "IMS Tables select / read (GU / GNP)", "content": "{{select_ims_tables_table}}"}}, {"level_4": {"caption": "IMS Tables Insert (ISRT)", "content": "{{insert_ims_tables_table}}"}}, {"level_4": {"caption": "IMS Tables Replace / Update (REPL)", "content": "{{update_ims_tables_table}}"}}]}}, {"level_3": {"caption": "Called sub-program modules", "content": "{{called_external_programs}}"}}, {"level_3": {"caption": "Output", "content": "{{output_parameters_table}}"}}, {"level_3": {"caption": "Exception scenarios", "content": "{{exception_scenarios}}"}}]}}]}}